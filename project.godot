; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="crafterio"
run/main_scene="uid://dq0nxjiipwin0"
config/features=PackedStringArray("4.4", "C#", "Mobile")
config/icon="res://icon.svg"

[autoload]

ResourcesManager="*res://scenes/ResourcesManager.tscn"
TextureManager="*res://scenes/TextureManager.tscn"
CommonSignals="*res://scenes/CommonSignals.tscn"
LevelManager="*res://scenes/LevelManager.tscn"

[display]

window/size/viewport_width=1920
window/size/viewport_height=1080
window/size/mode=3
window/stretch/scale=2.5

[dotnet]

project/assembly_name="crafterio"

[internationalization]

locale/translations=PackedStringArray("res://data/translations.de.translation", "res://data/translations.en.translation", "res://data/translations.es.translation", "res://data/translations.fr.translation", "res://data/translations.pl.translation")

[rendering]

renderer/rendering_method="mobile"
