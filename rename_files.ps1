# Rename files in Base Character PNG directory
$characterDir = "C:/dev/repos/crafterio/resources/solaria/SpritePack/Base/Base Character PNG"
$toolsDir = "C:/dev/repos/crafterio/resources/solaria/SpritePack/Base/Base Tools PNG"

# Process Character PNG files
Write-Host "Processing files in Base Character PNG directory..."
Get-ChildItem -Path $characterDir -File | ForEach-Object {
    $newName = $_.Name -replace "^Base ", ""
    $newPath = Join-Path -Path $_.DirectoryName -ChildPath $newName
    
    # Rename the file
    Rename-Item -Path $_.FullName -NewName $newName
    Write-Host "Renamed: $($_.Name) -> $newName"
    
    # If it's an import file, update its contents
    if ($_.Extension -eq ".import") {
        $content = Get-Content -Path $newPath -Raw
        $oldFileName = $_.Name -replace "\.import$", ""
        $newFileName = $newName -replace "\.import$", ""
        $updatedContent = $content -replace [regex]::Escape($oldFileName), $newFileName
        Set-Content -Path $newPath -Value $updatedContent
        Write-Host "  Updated import file content for $newName"
    }
}

# Process Tools PNG directories first
Write-Host "`nProcessing directories in Base Tools PNG directory..."
Get-ChildItem -Path $toolsDir -Directory | ForEach-Object {
    $newName = $_.Name -replace "^Base ", ""
    $newPath = Join-Path -Path $_.DirectoryName -ChildPath $newName
    
    # Rename the directory
    Rename-Item -Path $_.FullName -NewName $newName
    Write-Host "Renamed directory: $($_.Name) -> $newName"
}

# Process files in all subdirectories of Tools PNG
Write-Host "`nProcessing files in Base Tools PNG subdirectories..."
Get-ChildItem -Path $toolsDir -Recurse -File | ForEach-Object {
    $newName = $_.Name -replace "^Base ", ""
    $newPath = Join-Path -Path $_.DirectoryName -ChildPath $newName
    
    # Rename the file
    Rename-Item -Path $_.FullName -NewName $newName
    Write-Host "Renamed: $($_.Name) -> $newName"
    
    # If it's an import file, update its contents
    if ($_.Extension -eq ".import") {
        $content = Get-Content -Path $newPath -Raw
        $oldFileName = $_.Name -replace "\.import$", ""
        $newFileName = $newName -replace "\.import$", ""
        $updatedContent = $content -replace [regex]::Escape($oldFileName), $newFileName
        Set-Content -Path $newPath -Value $updatedContent
        Write-Host "  Updated import file content for $newName"
    }
}

Write-Host "`nRenaming complete!" 