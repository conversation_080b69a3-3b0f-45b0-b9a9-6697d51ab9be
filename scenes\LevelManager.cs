using Godot;
using System;
using System.Collections.Generic;

public partial class LevelManager : Node
{
	public static LevelManager Instance { get; private set; }
	
	private static readonly Dictionary<int, int> LevelXpRequirements = new Dictionary<int, int>
	{
		{ 2, 100 },
		{ 3, 250 },
		{ 4, 450 },
		{ 5, 700 },
		{ 6, 1000 },
		{ 7, 1350 },
		{ 8, 1750 },
		{ 9, 2200 },
		{ 10, 2700 },
		{ 11, 3250 },
		{ 12, 3850 },
		{ 13, 4500 },
		{ 14, 5200 },
		{ 15, 5950 },
		{ 16, 6750 },
		{ 17, 7600 },
		{ 18, 8500 },
		{ 19, 9450 },
		{ 20, 10450 },
		{ 21, 11500 },
		{ 22, 12600 },
		{ 23, 13750 },
		{ 24, 14950 },
		{ 25, 16200 },
		{ 26, 17500 },
		{ 27, 18850 },
		{ 28, 20250 },
		{ 29, 21700 },
		{ 30, 23200 },
		{ 31, 24750 },
		{ 32, 26350 },
		{ 33, 28000 },
		{ 34, 29700 },
		{ 35, 31450 },
		{ 36, 33250 },
		{ 37, 35100 },
		{ 38, 37000 },
		{ 39, 38950 },
		{ 40, 40950 },
		{ 41, 43000 },
		{ 42, 45100 },
		{ 43, 47250 },
		{ 44, 49450 },
		{ 45, 51700 },
		{ 46, 54000 },
		{ 47, 56350 },
		{ 48, 58750 },
		{ 49, 61200 },
		{ 50, 63700 }
	};

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
			
			if (CommonSignals.Instance != null)
			{
				CommonSignals.Instance.AddXp += OnAddXp;
			}
			
			GD.Print("LevelManager initialized and ready");
		}
		else
		{
			QueueFree();
		}
	}

	private void OnAddXp(int xpAmount)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		int currentLevel = resourcesManager.GetLevel();
		int currentXp = resourcesManager.GetExperience();
		int newXp = currentXp + xpAmount;
		
		resourcesManager.SetExperience(newXp);
		
		CheckForLevelUp(currentLevel, newXp);
	}

	private void CheckForLevelUp(int currentLevel, int currentXp)
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return;

		int newLevel = CalculateLevelFromXp(currentXp);
		
		if (newLevel > currentLevel)
		{
			int levelsGained = newLevel - currentLevel;
			int perkPointsGained = levelsGained * 2;
			
			resourcesManager.SetLevel(newLevel);
			resourcesManager.ModifyPerkPoints(perkPointsGained);
			
			GD.Print($"Level up! New level: {newLevel}, Perk points gained: {perkPointsGained}");
		}
	}

	private int CalculateLevelFromXp(int xp)
	{
		int level = 1;
		
		foreach (var kvp in LevelXpRequirements)
		{
			if (xp >= kvp.Value)
			{
				level = kvp.Key;
			}
			else
			{
				break;
			}
		}
		
		return level;
	}

	public static int GetXpRequiredForLevel(int level)
	{
		if (level <= 1) return 0;
		if (level > 50) return LevelXpRequirements[50];
		
		return LevelXpRequirements.TryGetValue(level, out int xp) ? xp : 0;
	}

	public static int GetXpRequiredForNextLevel(int currentLevel)
	{
		if (currentLevel >= 50) return GetXpRequiredForLevel(50);
		
		return GetXpRequiredForLevel(currentLevel + 1);
	}

	public static float GetLevelProgress(int currentLevel, int currentXp)
	{
		if (currentLevel >= 50) return 1.0f;
		
		int currentLevelXp = GetXpRequiredForLevel(currentLevel);
		int nextLevelXp = GetXpRequiredForNextLevel(currentLevel);
		
		if (nextLevelXp <= currentLevelXp) return 1.0f;
		
		int xpInCurrentLevel = currentXp - currentLevelXp;
		int xpNeededForNextLevel = nextLevelXp - currentLevelXp;
		
		return Mathf.Clamp((float)xpInCurrentLevel / xpNeededForNextLevel, 0.0f, 1.0f);
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.AddXp -= OnAddXp;
		}
	}
}
