using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json;

public partial class ResourcesManager : Node
{
	// Singleton instance
	public static ResourcesManager Instance { get; private set; }

	// Game data
	public GameSaveData GameData { get; private set; } = new GameSaveData();

	// Auto-save settings
	[Export] public float AutoSaveIntervalMinutes { get; set; } = 1.0f;
	[Export] public string DefaultSaveFileName { get; set; } = "autosave";
	[Export] public bool EnableAutoSave { get; set; } = true;

	// Events
	public event EventHandler<ResourceChangedEventArgs> ResourceChanged;
	public event EventHandler<StatChangedEventArgs> StatChanged;
	public event EventHandler GameSaved;
	public event EventHandler GameLoaded;

	// Private fields
	private Timer _autoSaveTimer;
	private DateTime _gameStartTime;
	private bool _isInitialized = false;

	public override void _Ready()
	{
		if (Instance == null)
		{
			Instance = this;
			_gameStartTime = DateTime.Now;

			SetupAutoSaveTimer();
			LoadGame(DefaultSaveFileName);
			InitializeQuickSelectItems();
			InitializeDefaultToolLevels();

			_isInitialized = true;
			GD.Print("ResourcesManager initialized and ready");
		}
		else
		{
			QueueFree();
		}
	}

	public override void _ExitTree()
	{
		// Always save when exiting (regardless of auto-save setting)
		if (_isInitialized)
		{
			// Collect current data from all active game objects before saving
			CollectCurrentGameState();
			SaveGame(DefaultSaveFileName);
		}
	}

	private void CollectCurrentGameState()
	{
		var playerController = FindPlayerController();
		if (playerController != null)
		{
			SetPlayerPosition(playerController.GlobalPosition);
			SetPlayerDirection(playerController.GetCurrentDirection());
			SetSelectedTool(playerController.GetCurrentTool());

			var customDataManager = playerController.GetCustomDataManager();
			if (customDataManager != null)
			{
				SaveCustomLayerData(customDataManager);
			}
		}

		// Save rabbits from all region managers
		SaveAllRegionRabbits();

		// Save object health data from all region managers
		SaveAllRegionObjectHealth();
	}
	private PlayerController FindPlayerController()
	{
		// Search for PlayerController in the current scene
		var currentScene = GetTree()?.CurrentScene;
		if (currentScene != null)
		{
			return FindPlayerControllerRecursive(currentScene);
		}
		return null;
	}

	private PlayerController FindPlayerControllerRecursive(Node node)
	{
		if (node is PlayerController playerController)
		{
			return playerController;
		}

		foreach (Node child in node.GetChildren())
		{
			var result = FindPlayerControllerRecursive(child);
			if (result != null)
				return result;
		}

		return null;
	}

	private void SetupAutoSaveTimer()
	{
		_autoSaveTimer = new Timer();
		_autoSaveTimer.WaitTime = AutoSaveIntervalMinutes * 60.0f; // Convert minutes to seconds
		_autoSaveTimer.Autostart = EnableAutoSave;
		_autoSaveTimer.Timeout += OnAutoSaveTimeout;
		AddChild(_autoSaveTimer);
	}

	private void OnAutoSaveTimeout()
	{
		if (EnableAutoSave)
		{
			SaveGame(DefaultSaveFileName);
		}
	}

	public bool SaveGame(string filename = null)
	{
		filename ??= DefaultSaveFileName;

		// Update save metadata
		GameData.SaveTime = DateTime.Now;
		GameData.PlayTimeSeconds += (int)(DateTime.Now - _gameStartTime).TotalSeconds;
		_gameStartTime = DateTime.Now; // Reset for next calculation

		var success = SaveHandler.Save(GameData, filename);

		if (success)
		{
			GameSaved?.Invoke(this, EventArgs.Empty);
		}
		else
		{
			GD.PrintErr($"Failed to save game to {filename}");
		}

		return success;
	}

	public bool LoadGame(string filename = null)
	{
		filename ??= DefaultSaveFileName;

		var loadedData = SaveHandler.Load<GameSaveData>(filename);

		if (loadedData != null && loadedData.IsValid())
		{
			GameData = loadedData;
			_gameStartTime = DateTime.Now; // Reset play time tracking

			GameLoaded?.Invoke(this, EventArgs.Empty);
			return true;
		}
		else
		{
			GameData = new GameSaveData(); // Use default data
			return false;
		}
	}
	public void ForceSave(string filename = null)
	{
		SaveGame(filename);
	}

	public float GetHealth() => GameData.PlayerStats.Health;

	public void SetHealth(float value)
	{
		float oldValue = GameData.PlayerStats.Health;
		GameData.PlayerStats.Health = Mathf.Clamp(value, 0, GameData.PlayerStats.MaxHealth);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Health",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Health,
			ChangeAmount = GameData.PlayerStats.Health - oldValue
		});
	}

	public void ModifyHealth(float amount)
	{
		SetHealth(GameData.PlayerStats.Health + amount);
	}

	public float GetEnergy() => GameData.PlayerStats.Energy;

	public void SetEnergy(float value)
	{
		float oldValue = GameData.PlayerStats.Energy;
		GameData.PlayerStats.Energy = Mathf.Clamp(value, 0, GameData.PlayerStats.MaxEnergy);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Energy",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Energy,
			ChangeAmount = GameData.PlayerStats.Energy - oldValue
		});
	}

	public void ModifyEnergy(float amount)
	{
		SetEnergy(GameData.PlayerStats.Energy + amount);
	}

	public int GetMoney() => GameData.PlayerStats.Money;

	public void SetMoney(int value)
	{
		int oldValue = GameData.PlayerStats.Money;
		GameData.PlayerStats.Money = Mathf.Max(0, value);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Money",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Money,
			ChangeAmount = GameData.PlayerStats.Money - oldValue
		});
	}

	public void ModifyMoney(int amount)
	{
		SetMoney(GameData.PlayerStats.Money + amount);
	}

	public float GetFood() => GameData.PlayerStats.Food;

	public void SetFood(float value)
	{
		float oldValue = GameData.PlayerStats.Food;
		GameData.PlayerStats.Food = Mathf.Clamp(value, 0, GameData.PlayerStats.MaxFood);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Food",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Food,
			ChangeAmount = GameData.PlayerStats.Food - oldValue
		});
	}

	public void ModifyFood(float amount)
	{
		SetFood(GameData.PlayerStats.Food + amount);
	}

	public float GetWater() => GameData.PlayerStats.Water;

	public void SetWater(float value)
	{
		float oldValue = GameData.PlayerStats.Water;
		GameData.PlayerStats.Water = Mathf.Clamp(value, 0, GameData.PlayerStats.MaxWater);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Water",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Water,
			ChangeAmount = GameData.PlayerStats.Water - oldValue
		});
	}

	public void ModifyWater(float amount)
	{
		SetWater(GameData.PlayerStats.Water + amount);
	}

	public int GetLevel() => GameData.PlayerStats.Level;

	public void SetLevel(int value)
	{
		int oldValue = GameData.PlayerStats.Level;
		GameData.PlayerStats.Level = Mathf.Max(1, value);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Level",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Level,
			ChangeAmount = GameData.PlayerStats.Level - oldValue
		});
	}

	public int GetExperience() => GameData.PlayerStats.Experience;

	public void SetExperience(int value)
	{
		int oldValue = GameData.PlayerStats.Experience;
		GameData.PlayerStats.Experience = Mathf.Max(0, value);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "Experience",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.Experience,
			ChangeAmount = GameData.PlayerStats.Experience - oldValue
		});
	}

	public int GetPerkPoints() => GameData.PlayerStats.PerkPoints;

	public void SetPerkPoints(int value)
	{
		int oldValue = GameData.PlayerStats.PerkPoints;
		GameData.PlayerStats.PerkPoints = Mathf.Max(0, value);

		StatChanged?.Invoke(this, new StatChangedEventArgs
		{
			StatName = "PerkPoints",
			OldValue = oldValue,
			NewValue = GameData.PlayerStats.PerkPoints,
			ChangeAmount = GameData.PlayerStats.PerkPoints - oldValue
		});
	}

	public void ModifyPerkPoints(int amount)
	{
		SetPerkPoints(GameData.PlayerStats.PerkPoints + amount);
	}

	public Vector2 GetPlayerPosition() => GameData.PlayerStats.Position;

	public void SetPlayerPosition(Vector2 position)
	{
		GameData.PlayerStats.Position = position;
	}


	public bool AddResource(ResourceType resourceType, int quantity = 1)
	{
		if (!CanAddResource(resourceType, quantity))
		{
			return false;
		}

		int oldQuantity = GameData.PlayerResources.GetResourceQuantity(resourceType);
		GameData.PlayerResources.AddResource(resourceType, quantity);
		int newQuantity = GameData.PlayerResources.GetResourceQuantity(resourceType);

		ResourceChanged?.Invoke(this, new ResourceChangedEventArgs
		{
			ResourceType = resourceType,
			OldValue = oldQuantity,
			NewValue = newQuantity,
			ChangeAmount = quantity
		});

		UpdateQuickSelectResourceQuantities();
		return true;
	}

	public bool RemoveResource(ResourceType resourceType, int quantity = 1)
	{
		int oldQuantity = GameData.PlayerResources.GetResourceQuantity(resourceType);
		bool success = GameData.PlayerResources.RemoveResource(resourceType, quantity);

		if (success)
		{
			int newQuantity = GameData.PlayerResources.GetResourceQuantity(resourceType);
			ResourceChanged?.Invoke(this, new ResourceChangedEventArgs
			{
				ResourceType = resourceType,
				OldValue = oldQuantity,
				NewValue = newQuantity,
				ChangeAmount = -quantity
			});

			UpdateQuickSelectResourceQuantities();
		}

		return success;
	}

	public int GetResourceQuantity(ResourceType resourceType)
	{
		return GameData.PlayerResources.GetResourceQuantity(resourceType);
	}

	public bool HasResource(ResourceType resourceType, int quantity = 1)
	{
		return GameData.PlayerResources.HasResource(resourceType, quantity);
	}

	public bool CanAddResource(ResourceType resourceType, int quantity = 1)
	{
		const int MAX_QUANTITY_PER_SLOT = 9999;
		int currentQuantity = GetResourceQuantity(resourceType);

		if (currentQuantity > 0)
		{
			return (currentQuantity + quantity) <= MAX_QUANTITY_PER_SLOT;
		}

		int maxSlots = GetMaxInventorySlots();
		int usedSlots = GetUsedInventorySlots();

		return usedSlots < maxSlots;
	}

	public int GetUsedInventorySlots()
	{
		return GameData.PlayerResources.Resources.Count;
	}

	public bool IsInventoryFull()
	{
		return GetUsedInventorySlots() >= GetMaxInventorySlots();
	}

	public ToolType GetSelectedTool() => GameData.PlayerStats.SelectedTool;

	public void SetSelectedTool(ToolType toolType)
	{
		GameData.PlayerStats.SelectedTool = toolType;
	}

	public int GetToolLevel(ToolType toolType)
	{
		if (GameData.PlayerStats.ToolLevels.TryGetValue(toolType, out int level))
		{
			return level;
		}
		return 1;
	}

	public void SetToolLevel(ToolType toolType, int level)
	{
		GameData.PlayerStats.ToolLevels[toolType] = Math.Max(1, level);
	}

	public int GetPickaxeDamage()
	{
		int level = GetToolLevel(ToolType.Pickaxe);
		return level + 2;
	}

	public int GetHammerPower()
	{
		int level = GetToolLevel(ToolType.Hammer);
		return 2 + level;
	}

	public int GetHoeLevel()
	{
		return GetToolLevel(ToolType.Hoe);
	}

	public int GetWateringCanLevel()
	{
		return GetToolLevel(ToolType.WateringCan);
	}

	private void InitializeDefaultToolLevels()
	{
		if (GameData.PlayerStats.ToolLevels.Count == 0)
		{
			GameData.PlayerStats.ToolLevels[ToolType.Pickaxe] = 1;
			GameData.PlayerStats.ToolLevels[ToolType.Hammer] = 1;
			GameData.PlayerStats.ToolLevels[ToolType.Hoe] = 1;
			GameData.PlayerStats.ToolLevels[ToolType.WateringCan] = 1;
			GameData.PlayerStats.ToolLevels[ToolType.Sword] = 1;
			GameData.PlayerStats.ToolLevels[ToolType.Bow] = 1;
		}
	}

	public string GetPlayerDirection() => GameData.PlayerStats.LastDirection;

	public void SetPlayerDirection(string direction)
	{
		GameData.PlayerStats.LastDirection = direction;
	}

	public void SaveCustomLayerData(CustomDataLayerManager customDataManager)
	{
		if (customDataManager != null)
		{
			string jsonData = customDataManager.SaveToJson();
			// Parse the JSON string to an object to avoid double encoding
			try
			{
				var jsonObject = JsonSerializer.Deserialize<object>(jsonData);
				GameData.WorldData.CustomLayerData["tileData"] = jsonObject;
			}
			catch (Exception ex)
			{
				GD.PrintErr($"Failed to parse custom layer data JSON: {ex.Message}");
				// Fallback to storing as string (will cause double encoding but won't crash)
				GameData.WorldData.CustomLayerData["tileData"] = jsonData;
			}
			GameData.WorldData.LastSaved = DateTime.Now;
		}
	}

	public void LoadCustomLayerData(CustomDataLayerManager customDataManager)
	{
		if (customDataManager != null && GameData.WorldData.CustomLayerData.ContainsKey("tileData"))
		{
			var tileDataValue = GameData.WorldData.CustomLayerData["tileData"];
			string jsonData;

			// Handle both old format (string) and new format (object)
			if (tileDataValue is string stringData)
			{
				// Old format: already a JSON string
				jsonData = stringData;
			}
			else
			{
				// New format: object that needs to be serialized to JSON
				try
				{
					jsonData = JsonSerializer.Serialize(tileDataValue);
				}
				catch (Exception ex)
				{
					GD.PrintErr($"Failed to serialize custom layer data: {ex.Message}");
					return;
				}
			}

			customDataManager.LoadFromJson(jsonData);

			// Clean up bloated save data after loading
			CleanupBloatedSaveData(customDataManager);
		}
	}

	/// <summary>
	/// Clean up bloated save data by removing tiles that only have region assignments
	/// and no actual objects or modifications, and fix corrupted coordinates
	/// </summary>
	private void CleanupBloatedSaveData(CustomDataLayerManager customDataManager)
	{
		GD.Print("ResourcesManager: Cleaning up bloated save data...");

		// Check if we have corrupted coordinate data
		if (GameData.WorldData.CustomLayerData.ContainsKey("tileData"))
		{
			var tileDataValue = GameData.WorldData.CustomLayerData["tileData"];
			string jsonData;

			if (tileDataValue is string stringData)
			{
				jsonData = stringData;
			}
			else
			{
				try
				{
					jsonData = JsonSerializer.Serialize(tileDataValue);
				}
				catch
				{
					jsonData = "{}";
				}
			}
		}

		// Force a save with the new HasNonDefaultValues logic
		// This will automatically exclude tiles that only have region assignments
		SaveCustomLayerData(customDataManager);

		GD.Print("ResourcesManager: Save data cleanup complete");
	}

	public void SetWorldData(string key, object value)
	{
		GameData.WorldData.TileData[key] = value;
	}

	public T GetWorldData<T>(string key, T defaultValue = default(T))
	{
		if (GameData.WorldData.TileData.ContainsKey(key))
		{
			try
			{
				return (T)GameData.WorldData.TileData[key];
			}
			catch
			{
				return defaultValue;
			}
		}
		return defaultValue;
	}

	public int GetPlayTimeSeconds()
	{
		return GameData.PlayTimeSeconds + (int)(DateTime.Now - _gameStartTime).TotalSeconds;
	}

	public string GetPlayTimeFormatted()
	{
		int totalSeconds = GetPlayTimeSeconds();
		int hours = totalSeconds / 3600;
		int minutes = (totalSeconds % 3600) / 60;
		int seconds = totalSeconds % 60;

		return $"{hours:D2}:{minutes:D2}:{seconds:D2}";
	}

	public void ResetGameData()
	{
		GameData = new GameSaveData();
		_gameStartTime = DateTime.Now;
		GD.Print("Game data reset to defaults");
	}

	public bool IsAutoSaveActive()
	{
		return EnableAutoSave && _autoSaveTimer != null && !_autoSaveTimer.IsStopped();
	}
	
	public void SetAutoSaveInterval(float minutes)
	{
		AutoSaveIntervalMinutes = minutes;
		if (_autoSaveTimer != null)
		{
			_autoSaveTimer.WaitTime = minutes * 60.0f;
		}
	}

	public string AddDroppedResource(Vector2 position, ResourceType resourceType, int quantity)
	{
		var droppedResource = new DroppedResourceData
		{
			X = position.X,
			Y = position.Y,
			ResourceType = resourceType,
			Quantity = quantity
		};
		GameData.WorldData.DroppedResources.Add(droppedResource);
		return droppedResource.Id;
	}

	public bool RemoveDroppedResourceById(string id)
	{
		int removedCount = GameData.WorldData.DroppedResources.RemoveAll(dr => dr.Id == id);
		return removedCount > 0;
	}

	public List<DroppedResourceData> GetDroppedResources()
	{
		return new List<DroppedResourceData>(GameData.WorldData.DroppedResources);
	}

	public void ClearDroppedResources()
	{
		GameData.WorldData.DroppedResources.Clear();
	}

	public string AddBuilding(Vector2I topLeftTile, string buildingType, int buildingId)
	{
		var buildingData = new BuildingData
		{
			TopLeftX = topLeftTile.X,
			TopLeftY = topLeftTile.Y,
			BuildingType = buildingType,
			BuildingId = buildingId
		};
		GameData.WorldData.Buildings.Add(buildingData);
		return buildingData.Id;
	}

	public bool RemoveBuildingById(string id)
	{
		int removedCount = GameData.WorldData.Buildings.RemoveAll(b => b.Id == id);
		return removedCount > 0;
	}

	public List<BuildingData> GetBuildings()
	{
		return GameData.WorldData.Buildings;
	}
	public void ClearBuildings()
	{
		GameData.WorldData.Buildings.Clear();
	}

	public void InitializeQuickSelectItems()
	{
		if (GameData.PlayerStats.QuickSelectItems.Count == 0)
		{
			for (int i = 0; i < 10; i++)
			{
				GameData.PlayerStats.QuickSelectItems.Add(new QuickSelectItem());
			}

			SetQuickSelectTool(0, ToolType.Pickaxe);    // Slot 1 (index 0)
			SetQuickSelectTool(1, ToolType.Hoe);        // Slot 2 (index 1)
			SetQuickSelectTool(2, ToolType.WateringCan); // Slot 3 (index 2)
			SetQuickSelectTool(3, ToolType.Hammer);     // Slot 4 (index 3)
			SetQuickSelectTool(4, ToolType.Sword);      // Slot 5 (index 4)
			SetQuickSelectTool(5, ToolType.Bow);        // Slot 6 (index 5)
		}
	}

	public void SetQuickSelectTool(int slotIndex, ToolType toolType)
	{
		if (slotIndex < 0 || slotIndex >= 10) return;

		EnsureQuickSelectSlots();
		var item = GameData.PlayerStats.QuickSelectItems[slotIndex];
		item.IsEmpty = false;
		item.IsTool = true;
		item.ToolType = toolType;
		item.ResourceType = ResourceType.None; // Default, not used for tools
		item.Quantity = 0; // Not used for tools
	}

	public void SetQuickSelectResource(int slotIndex, ResourceType resourceType)
	{
		if (slotIndex < 0 || slotIndex >= 10) return;

		EnsureQuickSelectSlots();
		var item = GameData.PlayerStats.QuickSelectItems[slotIndex];
		item.IsEmpty = false;
		item.IsTool = false;
		item.ToolType = ToolType.None; // Not used for resources
		item.ResourceType = resourceType;
		item.Quantity = GetResourceQuantity(resourceType);
	}

	public void ClearQuickSelectSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= 10) return;

		EnsureQuickSelectSlots();
		var item = GameData.PlayerStats.QuickSelectItems[slotIndex];
		item.IsEmpty = true;
		item.IsTool = false;
		item.ToolType = ToolType.None;
		item.ResourceType = ResourceType.Wood;
		item.Quantity = 0;
	}

	public QuickSelectItem GetQuickSelectItem(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= 10) return new QuickSelectItem();

		EnsureQuickSelectSlots();
		return GameData.PlayerStats.QuickSelectItems[slotIndex];
	}

	public void UpdateQuickSelectResourceQuantities()
	{
		EnsureQuickSelectSlots();
		for (int i = 0; i < GameData.PlayerStats.QuickSelectItems.Count; i++)
		{
			var item = GameData.PlayerStats.QuickSelectItems[i];
			if (!item.IsEmpty && !item.IsTool)
			{
				int currentQuantity = GetResourceQuantity(item.ResourceType);
				item.Quantity = currentQuantity;

				// Remove item if quantity is 0
				if (currentQuantity == 0)
				{
					ClearQuickSelectSlot(i);
				}
			}
		}
	}

	private void EnsureQuickSelectSlots()
	{
		while (GameData.PlayerStats.QuickSelectItems.Count < 10)
		{
			GameData.PlayerStats.QuickSelectItems.Add(new QuickSelectItem());
		}
	}
	public Dictionary<ResourceType, int> GetAllResources()
	{
		return new Dictionary<ResourceType, int>(GameData.PlayerResources.Resources);
	}

	public int GetMaxInventorySlots()
	{
		return GameData.PlayerStats.MaxInventorySlots;
	}

	public void SetMaxInventorySlots(int maxSlots)
	{
		GameData.PlayerStats.MaxInventorySlots = maxSlots;
	}

	/// <summary>
	/// Save rabbit data for a specific region
	/// </summary>
	public void SaveRabbitData(int regionId, List<RabbitSaveData> rabbitData)
	{
		string key = $"rabbits_region_{regionId}";
		GameData.WorldData.CustomLayerData[key] = rabbitData;
		GD.Print($"ResourcesManager: Saved {rabbitData.Count} rabbits for region {regionId}");
	}

	/// <summary>
	/// Load rabbit data for a specific region
	/// </summary>
	public List<RabbitSaveData> LoadRabbitData(int regionId)
	{
		string key = $"rabbits_region_{regionId}";

		if (GameData.WorldData.CustomLayerData.TryGetValue(key, out var data))
		{
			try
			{
				// Handle both JsonElement and direct List<RabbitSaveData> cases
				if (data is JsonElement jsonElement)
				{
					var rabbitDataList = JsonSerializer.Deserialize<List<RabbitSaveData>>(jsonElement.GetRawText());
					GD.Print($"ResourcesManager: Loaded {rabbitDataList?.Count ?? 0} rabbits for region {regionId}");
					return rabbitDataList ?? new List<RabbitSaveData>();
				}
				else if (data is List<RabbitSaveData> directList)
				{
					GD.Print($"ResourcesManager: Loaded {directList.Count} rabbits for region {regionId} (direct)");
					return directList;
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"ResourcesManager: Failed to deserialize rabbit data for region {regionId}: {ex.Message}");
			}
		}

		GD.Print($"ResourcesManager: No rabbit data found for region {regionId}");
		return new List<RabbitSaveData>();
	}

	/// <summary>
	/// Save object health data for a specific region
	/// </summary>
	public void SaveObjectHealthData(int regionId, Dictionary<Vector2I, int> objectHealthData)
	{
		string key = $"object_health_region_{regionId}";
		GameData.WorldData.CustomLayerData[key] = objectHealthData;
		GD.Print($"ResourcesManager: Saved health data for {objectHealthData.Count} objects in region {regionId}");
	}

	/// <summary>
	/// Load object health data for a specific region
	/// </summary>
	public Dictionary<Vector2I, int> LoadObjectHealthData(int regionId)
	{
		string key = $"object_health_region_{regionId}";

		if (GameData.WorldData.CustomLayerData.TryGetValue(key, out var data))
		{
			try
			{
				// Handle both JsonElement and direct Dictionary cases
				if (data is JsonElement jsonElement)
				{
					var healthData = JsonSerializer.Deserialize<Dictionary<Vector2I, int>>(jsonElement.GetRawText());
					GD.Print($"ResourcesManager: Loaded health data for {healthData?.Count ?? 0} objects in region {regionId}");
					return healthData ?? new Dictionary<Vector2I, int>();
				}
				else if (data is Dictionary<Vector2I, int> directDict)
				{
					GD.Print($"ResourcesManager: Loaded health data for {directDict.Count} objects in region {regionId} (direct)");
					return directDict;
				}
			}
			catch (Exception ex)
			{
				GD.PrintErr($"ResourcesManager: Failed to deserialize object health data for region {regionId}: {ex.Message}");
			}
		}

		GD.Print($"ResourcesManager: No object health data found for region {regionId}");
		return new Dictionary<Vector2I, int>();
	}

	/// <summary>
	/// Save rabbits from all region managers
	/// </summary>
	private void SaveAllRegionRabbits()
	{
		// Find all region managers in the scene
		var currentScene = GetTree()?.CurrentScene;
		if (currentScene == null) return;

		var regionManagers = FindRegionManagersRecursive(currentScene);
		foreach (var regionManager in regionManagers)
		{
			regionManager.SaveRabbits();
		}

		GD.Print($"ResourcesManager: Saved rabbits from {regionManagers.Count} region managers");
	}

	/// <summary>
	/// Save object health data from all region managers
	/// </summary>
	private void SaveAllRegionObjectHealth()
	{
		// Find all region managers in the scene
		var currentScene = GetTree()?.CurrentScene;
		if (currentScene == null) return;

		var regionManagers = FindRegionManagersRecursive(currentScene);
		foreach (var regionManager in regionManagers)
		{
			regionManager.SaveObjectHealthData();
		}

		GD.Print($"ResourcesManager: Saved object health data from {regionManagers.Count} region managers");
	}

	/// <summary>
	/// Find all region managers in the scene tree
	/// </summary>
	private List<Region1Manager> FindRegionManagersRecursive(Node node)
	{
		var regionManagers = new List<Region1Manager>();

		if (node is Region1Manager regionManager)
		{
			regionManagers.Add(regionManager);
		}

		foreach (Node child in node.GetChildren())
		{
			regionManagers.AddRange(FindRegionManagersRecursive(child));
		}

		return regionManagers;
	}
}
