using Godot;

/// <summary>
/// Manages textures for all resource types in the game
/// This class should be added to AutoLoad in project settings
/// </summary>
public partial class TextureManager : Node
{
	// Singleton instance
	public static TextureManager Instance { get; private set; }

	// Resource textures (for UI, inventory, etc.) - set these in the editor
	[Export] public Texture2D WoodTexture { get; set; }
	[Export] public Texture2D StoneTexture { get; set; }
	[Export] public Texture2D NetTexture { get; set; }
	[Export] public Texture2D PlankTexture { get; set; }
	[Export] public Texture2D Stone2Texture { get; set; }
	[Export] public Texture2D BerryTexture { get; set; }
	[Export] public Texture2D LeafTexture { get; set; }

	// Resource icon textures (for dropped items, small displays) - set these in the editor
	[Export] public Texture2D WoodIconTexture { get; set; }
	[Export] public Texture2D StoneIconTexture { get; set; }
	[Export] public Texture2D NetIconTexture { get; set; }
	[Export] public Texture2D PlankIconTexture { get; set; }
	[Export] public Texture2D Stone2IconTexture { get; set; }
	[Export] public Texture2D BerryIconTexture { get; set; }
	[Export] public Texture2D LeafIconTexture { get; set; }

	// Tool textures (for UI panels) - set these in the editor
	[Export] public Texture2D PickaxeTexture { get; set; }
	[Export] public Texture2D HammerTexture { get; set; }
	[Export] public Texture2D WateringCanTexture { get; set; }
	[Export] public Texture2D HoeTexture { get; set; }
	[Export] public Texture2D SwordTexture { get; set; }
	[Export] public Texture2D BowTexture { get; set; }

	public override void _Ready()
	{
		// Set singleton instance
		if (Instance == null)
		{
			Instance = this;
			GD.Print("TextureManager initialized and ready");
		}
		else
		{
			// Prevent duplicate instances
			QueueFree();
		}
	}

	/// <summary>
	/// Get texture for a specific resource type (for UI, inventory, etc.)
	/// </summary>
	/// <param name="resourceType">The resource type to get texture for</param>
	/// <returns>Texture2D for the resource, or null if not found</returns>
	public Texture2D GetResourceTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodTexture,
			ResourceType.Stone => StoneTexture,
			ResourceType.Net => NetTexture,
			ResourceType.Plank => PlankTexture,
			ResourceType.Stone2 => Stone2Texture,
			ResourceType.Berry => BerryTexture,
			ResourceType.Leaf => LeafTexture,
			_ => null
		};
	}

	/// <summary>
	/// Get icon texture for a specific resource type (for dropped items, small displays)
	/// </summary>
	/// <param name="resourceType">The resource type to get icon texture for</param>
	/// <returns>Texture2D for the resource icon, or null if not found</returns>
	public Texture2D GetResourceIconTexture(ResourceType resourceType)
	{
		return resourceType switch
		{
			ResourceType.Wood => WoodIconTexture,
			ResourceType.Stone => StoneIconTexture,
			ResourceType.Net => NetIconTexture,
			ResourceType.Plank => PlankIconTexture,
			ResourceType.Stone2 => Stone2IconTexture,
			ResourceType.Berry => BerryIconTexture,
			ResourceType.Leaf => LeafIconTexture,
			_ => null
		};
	}

	/// <summary>
	/// Check if a texture is available for the given resource type
	/// </summary>
	/// <param name="resourceType">The resource type to check</param>
	/// <returns>True if texture is available, false otherwise</returns>
	public bool HasTexture(ResourceType resourceType)
	{
		return GetResourceTexture(resourceType) != null;
	}

	/// <summary>
	/// Check if an icon texture is available for the given resource type
	/// </summary>
	/// <param name="resourceType">The resource type to check</param>
	/// <returns>True if icon texture is available, false otherwise</returns>
	public bool HasIconTexture(ResourceType resourceType)
	{
		return GetResourceIconTexture(resourceType) != null;
	}

	/// <summary>
	/// Get texture for a specific tool type
	/// </summary>
	/// <param name="toolType">The tool type to get texture for</param>
	/// <returns>Texture2D for the tool, or null if not found</returns>
	public Texture2D GetToolTexture(ToolType toolType)
	{
		return toolType switch
		{
			ToolType.Pickaxe => PickaxeTexture,
			ToolType.Hammer => HammerTexture,
			ToolType.Hoe => HoeTexture,
			ToolType.WateringCan => WateringCanTexture,
			ToolType.Sword => SwordTexture,
			ToolType.Bow => BowTexture,
			_ => null
		};
	}

	/// <summary>
	/// Check if a texture is available for the given tool type
	/// </summary>
	/// <param name="toolType">The tool type to check</param>
	/// <returns>True if texture is available, false otherwise</returns>
	public bool HasToolTexture(ToolType toolType)
	{
		return GetToolTexture(toolType) != null;
	}

	/// <summary>
	/// Get all available resource types that have textures
	/// </summary>
	/// <returns>Array of resource types with available textures</returns>
	public ResourceType[] GetAvailableResourceTypes()
	{
		var availableTypes = new System.Collections.Generic.List<ResourceType>();

		foreach (ResourceType resourceType in System.Enum.GetValues<ResourceType>())
		{
			if (HasTexture(resourceType))
			{
				availableTypes.Add(resourceType);
			}
		}

		return availableTypes.ToArray();
	}
}
