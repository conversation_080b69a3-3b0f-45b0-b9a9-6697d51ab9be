using Godot;

public partial class TilePlaceholder : Node2D
{
    private Sprite2D _placeholderSprite;
    private Vector2I _currentTilePosition = Vector2I.Zero;
    private bool _isVisible = false;

    [Export]
    public int TileSize { get; set; } = 16;

    public override void _Ready()
    {
        _placeholderSprite = GetNode<Sprite2D>("PlaceholderSprite");

        // Initially hidden
        Visible = false;
    }

    /// <summary>
    /// Show the placeholder at the specified tile position
    /// </summary>
    public void ShowAtTile(Vector2I tilePosition)
    {
        _currentTilePosition = tilePosition;

        // Convert tile position to world position
        Vector2 worldPosition = new Vector2(
            tilePosition.X * TileSize + TileSize / 2,
            tilePosition.Y * TileSize + TileSize / 2
        );

        GlobalPosition = worldPosition;

        if (!_isVisible)
        {
            _isVisible = true;
            Visible = true;
        }
    }

    /// <summary>
    /// Hide the placeholder
    /// </summary>
    public new void Hide()
    {
        if (_isVisible)
        {
            _isVisible = false;
            Visible = false;
        }
    }

    /// <summary>
    /// Get the current tile position where the placeholder is shown
    /// </summary>
    public Vector2I GetCurrentTilePosition()
    {
        return _currentTilePosition;
    }

    /// <summary>
    /// Check if the placeholder is currently visible
    /// </summary>
    public bool IsPlaceholderVisible()
    {
        return _isVisible;
    }
}
