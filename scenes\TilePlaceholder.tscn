[gd_scene load_steps=3 format=3 uid="uid://phpyt3e60ied"]

[ext_resource type="Script" uid="uid://7rd1eel3lvnw" path="res://scenes/TilePlaceholder.cs" id="1_placeholder_script"]
[ext_resource type="Texture2D" uid="uid://cs5f2jsaqtuxe" path="res://resources/solaria/UI/placeholderSingleField.png" id="2_placeholder_texture"]

[node name="TilePlaceholder" type="Node2D"]
scale = Vector2(0.7, 0.7)
script = ExtResource("1_placeholder_script")

[node name="PlaceholderSprite" type="Sprite2D" parent="."]
modulate = Color(1, 1, 1, 0.7)
texture = ExtResource("2_placeholder_texture")
