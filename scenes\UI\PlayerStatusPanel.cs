using Godot;
using System;

public partial class PlayerStatusPanel : CanvasLayer
{
	private ProgressBar _healthBar;
	private ProgressBar _energyBar;
	private ProgressBar _foodBar;
	private ProgressBar _waterBar;
	private ProgressBar _levelBar;
	private Label _goldLabel;
	private Label _levelLabel;
	private Label _percentageLabel;

	public override void _Ready()
	{
		GetNodes();
		ConnectSignals();
		UpdateAllBars();
	}

	private void GetNodes()
	{
		_healthBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarHealth");
		_energyBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarEnergy");
		_foodBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarFood");
		_waterBar = GetNode<ProgressBar>("TopLeftControl/Node2D/ProgressBarWater");
		_levelBar = GetNode<ProgressBar>("TopCenterControl/ProgressPanel/ProgressBarLevel");
		_goldLabel = GetNode<Label>("TopLeftControl/Node2D/GoldLabel");
		_levelLabel = GetNode<Label>("TopCenterControl/Level");
		_percentageLabel = GetNode<Label>("TopCenterControl/Percentage");
	}

	private void ConnectSignals()
	{
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.StatChanged += OnStatChanged;
		}
	}

	private void OnStatChanged(object sender, StatChangedEventArgs e)
	{
		switch (e.StatName)
		{
			case "Health":
				UpdateHealthBar();
				break;
			case "Energy":
				UpdateEnergyBar();
				break;
			case "Food":
				UpdateFoodBar();
				break;
			case "Water":
				UpdateWaterBar();
				break;
			case "Money":
				UpdateGoldLabel();
				break;
			case "Level":
			case "Experience":
				UpdateLevelBar();
				break;
		}
	}

	private void UpdateAllBars()
	{
		UpdateHealthBar();
		UpdateEnergyBar();
		UpdateFoodBar();
		UpdateWaterBar();
		UpdateLevelBar();
		UpdateGoldLabel();
	}

	private void UpdateHealthBar()
	{
		if (_healthBar == null || ResourcesManager.Instance == null) return;

		var rm = ResourcesManager.Instance;
		float healthPercentage = rm.GetHealth() / rm.GameData.PlayerStats.MaxHealth;
		_healthBar.SetProgress(healthPercentage);
	}

	private void UpdateEnergyBar()
	{
		if (_energyBar == null || ResourcesManager.Instance == null) return;

		var rm = ResourcesManager.Instance;
		float energyPercentage = rm.GetEnergy() / rm.GameData.PlayerStats.MaxEnergy;
		_energyBar.SetProgress(energyPercentage);
	}

	private void UpdateFoodBar()
	{
		if (_foodBar == null || ResourcesManager.Instance == null) return;

		var rm = ResourcesManager.Instance;
		float foodPercentage = rm.GetFood() / rm.GameData.PlayerStats.MaxFood;
		_foodBar.SetProgress(foodPercentage);
	}

	private void UpdateWaterBar()
	{
		if (_waterBar == null || ResourcesManager.Instance == null) return;

		var rm = ResourcesManager.Instance;
		float waterPercentage = rm.GetWater() / rm.GameData.PlayerStats.MaxWater;
		_waterBar.SetProgress(waterPercentage);
	}

	private void UpdateLevelBar()
	{
		if (_levelBar == null || ResourcesManager.Instance == null) return;

		var rm = ResourcesManager.Instance;
		int currentLevel = rm.GetLevel();
		int currentXp = rm.GetExperience();

		_levelLabel.Text = $"LVL {currentLevel}";

		if (currentLevel >= 50)
		{
			_levelBar.SetProgress(1.0f);
			_percentageLabel.Text = "MAX";
		}
		else
		{
			float progress = LevelManager.GetLevelProgress(currentLevel, currentXp);
			_levelBar.SetProgress(progress);
			_percentageLabel.Text = $"{(progress * 100):F0}%";
		}
	}

	private void UpdateGoldLabel()
	{
		if (_goldLabel == null || ResourcesManager.Instance == null) return;

		int money = ResourcesManager.Instance.GetMoney();
		_goldLabel.Text = money.ToString();
	}

	public override void _ExitTree()
	{
		if (ResourcesManager.Instance != null)
		{
			ResourcesManager.Instance.StatChanged -= OnStatChanged;
		}
	}
}
