[gd_scene load_steps=13 format=3 uid="uid://bnxwvx42a6onm"]

[ext_resource type="Texture2D" uid="uid://b5xvavw7auuh1" path="res://resources/solaria/UI/PlayerStatsBar2.png" id="1_f7eds"]
[ext_resource type="Script" uid="uid://d072eubcfjtx5" path="res://scenes/UI/PlayerStatusPanel.cs" id="1_player_status_panel"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="2_aen1g"]
[ext_resource type="Texture2D" uid="uid://bfi0et2sl2ss1" path="res://resources/solaria/UI/progress/progressBgBig.png" id="2_g4dys"]
[ext_resource type="Texture2D" uid="uid://dj55cdf6dge70" path="res://resources/solaria/UI/progress/progressBarGreenBig.png" id="3_mrbjg"]
[ext_resource type="Texture2D" uid="uid://b1fy0xoq3a2ek" path="res://resources/solaria/UI/progress/progressBgSmall.png" id="3_uekvw"]
[ext_resource type="Texture2D" uid="uid://l5vb0n6285pr" path="res://resources/solaria/UI/progress/progressFrontRedHorizontalSmall.png" id="4_kmqun"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="4_mrbjg"]
[ext_resource type="Texture2D" uid="uid://bnx4e0d8qnvnu" path="res://resources/solaria/UI/progress/progressFrontYellow2HorizontalSmall.png" id="5_kmqun"]
[ext_resource type="Texture2D" uid="uid://byrgsib48t8ug" path="res://resources/solaria/UI/progress/progressFrontOrangeHorizontalSmall.png" id="6_38xxw"]
[ext_resource type="Texture2D" uid="uid://b4nl10nyoyvib" path="res://resources/solaria/UI/progress/progressFrontBlueHorizontalSmall.png" id="7_qdjw5"]
[ext_resource type="Texture2D" uid="uid://c0c8f1yb5fb2e" path="res://resources/solaria/UI/progress/progressFrontWhiteHorizontalSmall.png" id="8_7qmhh"]

[node name="PlayerStatusPanel" type="CanvasLayer"]
script = ExtResource("1_player_status_panel")

[node name="TopLeftControl" type="Control" parent="."]
layout_mode = 3
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="Node2D" type="Node2D" parent="TopLeftControl"]

[node name="PanelIcons" type="Sprite2D" parent="TopLeftControl/Node2D"]
position = Vector2(11, 43)
texture = ExtResource("1_f7eds")

[node name="ProgressBarHealth" parent="TopLeftControl/Node2D" instance=ExtResource("2_aen1g")]
position = Vector2(37, 11)
texture = ExtResource("3_uekvw")
FrontTexture = ExtResource("4_kmqun")
TotalWidth = 48
MarginLeft = 0
MarginRight = 0

[node name="ProgressBarEnergy" parent="TopLeftControl/Node2D" instance=ExtResource("2_aen1g")]
position = Vector2(37, 27.31)
texture = ExtResource("3_uekvw")
FrontTexture = ExtResource("5_kmqun")
TotalWidth = 48
MarginLeft = 0
MarginRight = 0

[node name="ProgressBarFood" parent="TopLeftControl/Node2D" instance=ExtResource("2_aen1g")]
position = Vector2(37, 43.73)
texture = ExtResource("3_uekvw")
FrontTexture = ExtResource("6_38xxw")
TotalWidth = 48
MarginLeft = 0
MarginRight = 0

[node name="ProgressBarWater" parent="TopLeftControl/Node2D" instance=ExtResource("2_aen1g")]
position = Vector2(37, 59.465)
texture = ExtResource("3_uekvw")
FrontTexture = ExtResource("7_qdjw5")
TotalWidth = 48
MarginLeft = 0
MarginRight = 0

[node name="GoldBackground" type="Sprite2D" parent="TopLeftControl/Node2D"]
modulate = Color(1, 1, 0.419608, 0.364706)
position = Vector2(50, 75.39)
scale = Vector2(2.01, 2)
texture = ExtResource("8_7qmhh")

[node name="GoldLabel" parent="TopLeftControl/Node2D" instance=ExtResource("4_mrbjg")]
offset_left = 19.0
offset_top = 66.0
offset_right = 93.0
offset_bottom = 84.0
theme_override_colors/font_color = Color(0.118581, 0.118581, 0.118581, 1)
text = "1000"
horizontal_alignment = 0

[node name="TopCenterControl" type="Control" parent="."]
layout_mode = 3
anchors_preset = 5
anchor_left = 0.5
anchor_right = 0.5
offset_left = -20.0
offset_right = 20.0
offset_bottom = 40.0
grow_horizontal = 2

[node name="ProgressPanel" type="Node2D" parent="TopCenterControl"]
position = Vector2(0, 12)
scale = Vector2(1.5, 1.2)

[node name="ProgressBarLevel" parent="TopCenterControl/ProgressPanel" instance=ExtResource("2_aen1g")]
position = Vector2(1.205, 0)
scale = Vector2(1, 1.2)
texture = ExtResource("2_g4dys")
FrontTexture = ExtResource("3_mrbjg")
TotalWidth = 208
MarginLeft = 0
MarginRight = 0

[node name="Level" parent="TopCenterControl" instance=ExtResource("4_mrbjg")]
layout_mode = 0
offset_left = -145.0
offset_top = 5.0
offset_right = 271.0
offset_bottom = 24.0
scale = Vector2(0.7, 0.7)
theme_override_colors/font_color = Color(0.925353, 0.925353, 0.925353, 1)
text = "LVL 1"

[node name="Percentage" parent="TopCenterControl" instance=ExtResource("4_mrbjg")]
layout_mode = 0
offset_left = -141.0
offset_top = 5.0
offset_right = -98.0
offset_bottom = 24.0
scale = Vector2(0.7, 0.7)
theme_override_colors/font_color = Color(0.925353, 0.925353, 0.925353, 1)
text = "100%"
horizontal_alignment = 0
