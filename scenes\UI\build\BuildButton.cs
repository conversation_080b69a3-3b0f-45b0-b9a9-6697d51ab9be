using Godot;

public partial class BuildButton : Sprite2D
{
	private Button _button;
	private BuildMenu _buildMenu;

	public override void _Ready()
	{
		_button = GetNode<Button>("Button");
		if (_button == null)
		{
			return;
		}

		_buildMenu = GetNode<BuildMenu>("BuildMenu");
		if (_buildMenu == null)
		{
			return;
		}

		_button.Pressed += OnButtonPressed;
	}

	private void OnButtonPressed()
	{
		if (_buildMenu != null)
		{
			_buildMenu.OpenMenu();
		}
	}

	public override void _ExitTree()
	{
		if (_button != null)
		{
			_button.Pressed -= OnButtonPressed;
		}
	}
}
