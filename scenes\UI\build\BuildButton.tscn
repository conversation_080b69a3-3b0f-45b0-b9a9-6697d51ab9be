[gd_scene load_steps=15 format=3 uid="uid://mtskeuj17t5f"]

[ext_resource type="Texture2D" uid="uid://p2pm881aykdf" path="res://resources/solaria/UI/build/buildMenuOption.png" id="1_a0se7"]
[ext_resource type="Script" uid="uid://cle7ybgiqm05y" path="res://scenes/UI/build/BuildButton.cs" id="1_buildbutton"]
[ext_resource type="PackedScene" uid="uid://c1r8ujqauaqwx" path="res://scenes/UI/build/buildMenu.tscn" id="2_xpmvm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cjq2o"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_65ut4"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_uknsn"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3x5ic"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_f8v0k"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_qw04t"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6d5f7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_lposm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_m7glx"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_thjhm"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_vslyo"]

[node name="BuildButton" type="Sprite2D"]
texture = ExtResource("1_a0se7")
script = ExtResource("1_buildbutton")

[node name="Button" type="Button" parent="."]
offset_left = -16.0
offset_top = -16.0
offset_right = 16.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_cjq2o")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_65ut4")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_uknsn")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_3x5ic")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_f8v0k")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_qw04t")
theme_override_styles/hover = SubResource("StyleBoxEmpty_6d5f7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_lposm")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_m7glx")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_thjhm")
theme_override_styles/normal = SubResource("StyleBoxEmpty_vslyo")
metadata/_edit_use_anchors_ = true

[node name="BuildMenu" parent="." instance=ExtResource("2_xpmvm")]
