using Godot;

public partial class BuildMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _buildButton;
	private Sprite2D _buildButtonSprite;

	// Anvil building requirements
	private const int ANVIL_WOOD_REQUIRED = 5;
	private const int ANVIL_STONE_REQUIRED = 5;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("BuildMenu: AnimationPlayer node not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("BuildMenu: CloseButton node not found!");
			return;
		}

		_buildButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemList/Button");
		if (_buildButton == null)
		{
			GD.PrintErr("BuildMenu: Build button not found!");
			return;
		}

		_buildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemList/BuildButton");
		if (_buildButtonSprite == null)
		{
			GD.PrintErr("BuildMenu: BuildButton sprite not found!");
			return;
		}

		_closeButton.Pressed += OnCloseButtonPressed;
		_buildButton.Pressed += OnBuildButtonPressed;

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("RESET");
		}
	}

	public void OpenMenu()
	{
		// Update button state based on available resources
		UpdateBuildButtonState();

		// Disable player movement when build menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}
	}

	public void CloseMenu()
	{
		// Re-enable player movement when build menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void OnBuildButtonPressed()
	{
		// Check if player has enough resources
		if (!CanAffordAnvil())
		{
			GD.Print("BuildMenu: Not enough resources to build anvil!");
			return;
		}

		CloseMenu();

		var buildingPlacer = GetNode<BuildingPlacer>("/root/world/BuildingPlacer");
		if (buildingPlacer != null)
		{
			buildingPlacer.StartPlacingAnvil();
		}
		else
		{
			GD.PrintErr("BuildMenu: BuildingPlacer not found!");
		}
	}

	/// <summary>
	/// Check if player has enough resources to build an anvil
	/// </summary>
	private bool CanAffordAnvil()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		bool hasWood = resourcesManager.HasResource(ResourceType.Wood, ANVIL_WOOD_REQUIRED);
		bool hasStone = resourcesManager.HasResource(ResourceType.Stone, ANVIL_STONE_REQUIRED);

		return hasWood && hasStone;
	}

	/// <summary>
	/// Update the build button visual state based on available resources
	/// </summary>
	private void UpdateBuildButtonState()
	{
		bool canAfford = CanAffordAnvil();

		if (_buildButtonSprite != null)
		{
			// Gray out the button if can't afford
			if (canAfford)
			{
				_buildButtonSprite.Modulate = Colors.White; // Normal color
			}
			else
			{
				_buildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f); // Grayed out
			}
		}

		if (_buildButton != null)
		{
			_buildButton.Disabled = !canAfford;
		}
	}

	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
		if (_buildButton != null)
		{
			_buildButton.Pressed -= OnBuildButtonPressed;
		}
	}
}
