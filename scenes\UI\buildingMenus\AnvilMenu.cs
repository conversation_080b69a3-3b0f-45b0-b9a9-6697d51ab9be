using Godot;

public partial class AnvilMenu : CanvasLayer
{
	private AnimationPlayer _animationPlayer;
	private Button _closeButton;
	private Button _craftButton;
	private Sprite2D _buildButtonSprite;
	private Anvil _anvil;

	private const int PLANK_WOOD_REQUIRED = 2;

	public override void _Ready()
	{
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		if (_animationPlayer == null)
		{
			GD.PrintErr("AnvilMenu: AnimationPlayer not found!");
			return;
		}

		_closeButton = GetNode<Button>("Control/Panel/CloseButton");
		if (_closeButton == null)
		{
			GD.PrintErr("AnvilMenu: Close button not found!");
			return;
		}

		_craftButton = GetNode<Button>("Control/Panel/ScrollContainer/VBoxContainer/ItemList1/Button");
		if (_craftButton == null)
		{
			GD.PrintErr("AnvilMenu: Craft button not found!");
			return;
		}

		_buildButtonSprite = GetNode<Sprite2D>("Control/Panel/ScrollContainer/VBoxContainer/ItemList1/BuildButton");
		if (_buildButtonSprite == null)
		{
			GD.PrintErr("AnvilMenu: BuildButton sprite not found!");
			return;
		}

		_closeButton.Pressed += OnCloseButtonPressed;
		_craftButton.Pressed += OnCraftButtonPressed;
	}

	public void SetAnvil(Anvil anvil)
	{
		_anvil = anvil;
	}

	public void OpenMenu()
	{
		UpdateCraftButtonState();

		// Disable player movement when menu opens
		CommonSignals.Instance?.EmitPlayerMovementEnabled(false);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Open");
		}
	}

	private void OnCloseButtonPressed()
	{
		CloseMenu();
	}

	private void CloseMenu()
	{
		// Re-enable player movement when menu closes
		CommonSignals.Instance?.EmitPlayerMovementEnabled(true);

		if (_animationPlayer != null)
		{
			_animationPlayer.Play("Close");
		}
	}

	private void OnCraftButtonPressed()
	{
		if (!CanAffordPlank())
		{
			GD.Print("AnvilMenu: Not enough resources to craft plank!");
			return;
		}

		CloseMenu();

		if (_anvil != null)
		{
			_anvil.StartCraftingPlank();
		}
		else
		{
			GD.PrintErr("AnvilMenu: Anvil reference not set!");
		}
	}

	private bool CanAffordPlank()
	{
		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager == null) return false;

		return resourcesManager.HasResource(ResourceType.Wood, PLANK_WOOD_REQUIRED);
	}

	private void UpdateCraftButtonState()
	{
		bool canAfford = CanAffordPlank();
		
		if (_buildButtonSprite != null)
		{
			if (canAfford)
			{
				_buildButtonSprite.Modulate = Colors.White;
			}
			else
			{
				_buildButtonSprite.Modulate = new Color(0.6f, 0.6f, 0.6f, 1.0f);
			}
		}

		if (_craftButton != null)
		{
			_craftButton.Disabled = !canAfford;
		}
	}



	public override void _ExitTree()
	{
		if (_closeButton != null)
		{
			_closeButton.Pressed -= OnCloseButtonPressed;
		}
		if (_craftButton != null)
		{
			_craftButton.Pressed -= OnCraftButtonPressed;
		}
	}
}
