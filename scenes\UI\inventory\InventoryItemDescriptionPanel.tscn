[gd_scene load_steps=18 format=3 uid="uid://b5tfj26i77phc"]

[ext_resource type="Texture2D" uid="uid://dxp2ylgwp62ak" path="res://resources/solaria/UI/inventory/inventory_item_description2.png" id="1_5aa4r"]
[ext_resource type="Script" uid="uid://c8p14y8wjysk5" path="res://scenes/UI/inventory/InventoryItemDescriptionPanel.cs" id="1_inventory_item_description_panel"]
[ext_resource type="Texture2D" uid="uid://bgpd0wfpx3kvj" path="res://resources/solaria/UI/inventory/inventory_item_single_slot.png" id="2_1anlh"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="3_1anlh"]
[ext_resource type="PackedScene" uid="uid://brynlg0mgkf76" path="res://scenes/UI/common/Label.tscn" id="3_8wihx"]
[ext_resource type="Texture2D" uid="uid://da4knysjgbcm4" path="res://resources/solaria/UI/inventory/inventory_menu_button.png" id="4_urx46"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jxjru"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1anlh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8wihx"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_urx46"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5h3qs"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_sdxn7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_ajllj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8rp0g"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_pt6gj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_epqon"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_8m0e4"]

[node name="InventoryItemDescriptionPanel" type="Node2D"]
script = ExtResource("1_inventory_item_description_panel")

[node name="Panel" type="Sprite2D" parent="."]
scale = Vector2(0.95, 0.95)
texture = ExtResource("1_5aa4r")

[node name="SelectedItemFrame" type="Sprite2D" parent="Panel"]
position = Vector2(-32.4211, -41.8947)
texture = ExtResource("2_1anlh")

[node name="SelectedItem" type="Sprite2D" parent="Panel"]
position = Vector2(-32.4211, -41.8947)
texture = ExtResource("3_1anlh")

[node name="ResourceName" parent="Panel" instance=ExtResource("3_8wihx")]
offset_left = -16.0
offset_top = -58.0
offset_right = 93.0
offset_bottom = -6.0
scale = Vector2(0.63, 0.63)
text = "Wooden log"

[node name="DescriptionVariant1" type="Node2D" parent="Panel"]

[node name="ResourceName" parent="Panel/DescriptionVariant1" instance=ExtResource("3_8wihx")]
offset_left = -53.0
offset_top = -21.0
offset_right = 199.0
offset_bottom = 174.0
scale = Vector2(0.42, 0.42)
text = "Some random description of what this item does"
vertical_alignment = 0

[node name="DescriptionVariant2" type="Node2D" parent="Panel"]
visible = false

[node name="ResourceName" parent="Panel/DescriptionVariant2" instance=ExtResource("3_8wihx")]
offset_left = -45.0
offset_right = 193.0
offset_bottom = 161.0
scale = Vector2(0.38, 0.38)
text = "Some random description of what this item does"
vertical_alignment = 0

[node name="Button1" type="Sprite2D" parent="."]
position = Vector2(-26.12, 74.44)
scale = Vector2(0.84, 0.84)
texture = ExtResource("4_urx46")

[node name="Label" parent="Button1" instance=ExtResource("3_8wihx")]
offset_left = -29.0
offset_top = -14.0
offset_right = 61.0
offset_bottom = 29.0
scale = Vector2(0.65, 0.65)
text = "TEXT_BUTTON_USE"

[node name="Button" type="Button" parent="Button1"]
offset_left = -31.0
offset_top = -15.0
offset_right = 31.0
offset_bottom = 15.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_jxjru")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_1anlh")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_8wihx")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_urx46")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_5h3qs")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_sdxn7")
theme_override_styles/hover = SubResource("StyleBoxEmpty_ajllj")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8rp0g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_pt6gj")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_epqon")
theme_override_styles/normal = SubResource("StyleBoxEmpty_8m0e4")

[node name="Button2" type="Sprite2D" parent="."]
position = Vector2(27.88, 74.44)
scale = Vector2(0.84, 0.84)
texture = ExtResource("4_urx46")

[node name="Label" parent="Button2" instance=ExtResource("3_8wihx")]
offset_left = -29.0
offset_top = -14.0
offset_right = 61.0
offset_bottom = 29.0
scale = Vector2(0.65, 0.65)
text = "TEXT_BUTTON_REMOVE"

[node name="Button" type="Button" parent="Button2"]
offset_left = -31.0
offset_top = -15.0
offset_right = 31.0
offset_bottom = 15.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_jxjru")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_1anlh")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_8wihx")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_urx46")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_5h3qs")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_sdxn7")
theme_override_styles/hover = SubResource("StyleBoxEmpty_ajllj")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8rp0g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_pt6gj")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_epqon")
theme_override_styles/normal = SubResource("StyleBoxEmpty_8m0e4")

[node name="Button3" type="Sprite2D" parent="."]
position = Vector2(-26.12, 102.165)
scale = Vector2(0.84, 0.84)
texture = ExtResource("4_urx46")

[node name="Label" parent="Button3" instance=ExtResource("3_8wihx")]
offset_left = -29.0
offset_top = -14.0
offset_right = 61.0
offset_bottom = 29.0
scale = Vector2(0.65, 0.65)
text = "TEXT_BUTTON_QUICK_ACTIONS"

[node name="Button" type="Button" parent="Button3"]
offset_left = -31.0
offset_top = -15.0
offset_right = 31.0
offset_bottom = 15.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_jxjru")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_1anlh")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_8wihx")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_urx46")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_5h3qs")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_sdxn7")
theme_override_styles/hover = SubResource("StyleBoxEmpty_ajllj")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8rp0g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_pt6gj")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_epqon")
theme_override_styles/normal = SubResource("StyleBoxEmpty_8m0e4")

[node name="Button4" type="Sprite2D" parent="."]
position = Vector2(27.88, 102.165)
scale = Vector2(0.84, 0.84)
texture = ExtResource("4_urx46")

[node name="Label" parent="Button4" instance=ExtResource("3_8wihx")]
offset_left = -29.0
offset_top = -14.0
offset_right = 61.0
offset_bottom = 29.0
scale = Vector2(0.65, 0.65)
text = "TEXT_BUTTON_BUTTON4"

[node name="Button" type="Button" parent="Button4"]
offset_left = -31.0
offset_top = -15.0
offset_right = 31.0
offset_bottom = 15.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_jxjru")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_1anlh")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_8wihx")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_urx46")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_5h3qs")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_sdxn7")
theme_override_styles/hover = SubResource("StyleBoxEmpty_ajllj")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_8rp0g")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_pt6gj")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_epqon")
theme_override_styles/normal = SubResource("StyleBoxEmpty_8m0e4")
