[gd_scene load_steps=22 format=3 uid="uid://c7pcpuwwc2e7k"]

[ext_resource type="Texture2D" uid="uid://d3wb8u348lrx5" path="res://resources/solaria/UI/inventory/inventory_menu_bg.png" id="1_cna2i"]
[ext_resource type="Script" uid="uid://dwyho3rnfty53" path="res://scenes/UI/inventory/InventoryMenu.cs" id="1_inventory_menu"]
[ext_resource type="PackedScene" uid="uid://c1jafpqv5feno" path="res://scenes/UI/inventory/MenuSelectionSector.tscn" id="2_r0oy0"]
[ext_resource type="Texture2D" uid="uid://cdxye6tum1anb" path="res://resources/solaria/UI/inventory/close_button.png" id="3_iw7eh"]
[ext_resource type="PackedScene" uid="uid://b5tfj26i77phc" path="res://scenes/UI/inventory/InventoryItemDescriptionPanel.tscn" id="5_cna2i"]
[ext_resource type="PackedScene" uid="uid://dkrcy82dvgl5m" path="res://scenes/UI/inventory/InventoryTools.tscn" id="6_3dk6q"]

[sub_resource type="Animation" id="Animation_iw7eh"]
resource_name = "Close"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.03, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1, 1), Vector2(1.05, 1.05), Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0.09),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [false]
}

[sub_resource type="Animation" id="Animation_r0oy0"]
resource_name = "Open"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(0.95, 0.95), Vector2(1.05, 1.05), Vector2(1, 1)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="Animation" id="Animation_cna2i"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Control/Panel:scale")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0.95, 0.95)]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("Control/Panel:visible")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_v0ypl"]
_data = {
&"Close": SubResource("Animation_iw7eh"),
&"Open": SubResource("Animation_r0oy0"),
&"RESET": SubResource("Animation_cna2i")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_iw7eh"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_cna2i"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_v0ypl"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_0py87"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_6yq8u"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_y4sqg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_1gex7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_onvy6"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_3eglp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_fj7qj"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yd80n"]

[node name="InventoryMenu" type="CanvasLayer"]
script = ExtResource("1_inventory_menu")

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_v0ypl")
}

[node name="Control" type="Control" parent="."]
layout_mode = 3
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2

[node name="Panel" type="Sprite2D" parent="Control"]
scale = Vector2(0.95, 0.95)
texture = ExtResource("1_cna2i")

[node name="MenuSelectionSector1" parent="Control/Panel" instance=ExtResource("2_r0oy0")]
position = Vector2(0, -64)

[node name="MenuSelectionSector2" parent="Control/Panel" instance=ExtResource("2_r0oy0")]
position = Vector2(0, -32)

[node name="InventoryItemDescriptionPanel" parent="Control/Panel" instance=ExtResource("5_cna2i")]
position = Vector2(134.294, -27.613)

[node name="InventoryTools" parent="Control/Panel" instance=ExtResource("6_3dk6q")]
position = Vector2(-129, 0)

[node name="CloseButtonSprite" type="Sprite2D" parent="Control/Panel"]
position = Vector2(190.737, -90.3158)
texture = ExtResource("3_iw7eh")

[node name="CloseButton" type="Button" parent="Control/Panel"]
offset_left = 180.737
offset_top = -102.316
offset_right = 200.737
offset_bottom = -80.3158
theme_override_styles/focus = SubResource("StyleBoxEmpty_iw7eh")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_cna2i")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_v0ypl")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_0py87")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_6yq8u")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_y4sqg")
theme_override_styles/hover = SubResource("StyleBoxEmpty_1gex7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_onvy6")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_3eglp")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_fj7qj")
theme_override_styles/normal = SubResource("StyleBoxEmpty_yd80n")
