[gd_scene load_steps=3 format=3 uid="uid://dkrcy82dvgl5m"]

[ext_resource type="Texture2D" uid="uid://dmbeircvebfmo" path="res://resources/solaria/UI/inventory/inventory_menu_equipments_tools.png" id="1_jvnf0"]
[ext_resource type="PackedScene" uid="uid://dqr7mvn1e4m0b" path="res://scenes/UI/inventory/MenuSelectionSectorTools.tscn" id="2_jvnf0"]

[node name="InventoryTools" type="Node2D"]

[node name="Panel" type="Sprite2D" parent="."]
texture = ExtResource("1_jvnf0")

[node name="MenuSelectionSectorTools" parent="Panel" instance=ExtResource("2_jvnf0")]
