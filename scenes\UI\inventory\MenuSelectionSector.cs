using Godot;
using System;
using System.Collections.Generic;

public partial class MenuSelectionSector : Sprite2D
{
	private const int SLOTS_PER_SECTOR = 4;

	private Sprite2D[] _slotNodes = new Sprite2D[SLOTS_PER_SECTOR];
	private Sprite2D[] _selectedPlaceholders = new Sprite2D[SLOTS_PER_SECTOR];
	private Sprite2D[] _itemSprites = new Sprite2D[SLOTS_PER_SECTOR];
	private Label[] _amountLabels = new Label[SLOTS_PER_SECTOR];
	private AnimationPlayer[] _animationPlayers = new AnimationPlayer[SLOTS_PER_SECTOR];
	private Button[] _selectButtons = new Button[SLOTS_PER_SECTOR];

	private (ResourceType resourceType, int quantity, bool hasItem)[] _slotData =
		new (ResourceType, int, bool)[SLOTS_PER_SECTOR];

	private int _selectedSlot = -1;

	public event Action<MenuSelectionSector, int, ResourceType, int> ItemSelected;
	
	public override void _Ready()
	{
		InitializeSlotReferences();
		ClearAllSlots();
	}
	
	private void InitializeSlotReferences()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			string slotName = $"Slot{i + 1}";

			_slotNodes[i] = GetNode<Sprite2D>(slotName);
			_selectedPlaceholders[i] = GetNode<Sprite2D>($"{slotName}/SelectedPlaceholder");
			_itemSprites[i] = GetNode<Sprite2D>($"{slotName}/Item");
			_amountLabels[i] = GetNode<Label>($"{slotName}/AmountText");
			_animationPlayers[i] = GetNode<AnimationPlayer>($"{slotName}/AnimationPlayer");
			_selectButtons[i] = GetNode<Button>($"{slotName}/SelectItemButton");

			int slotIndex = i;
			_selectButtons[i].Pressed += () => OnSlotButtonPressed(slotIndex);

			_selectedPlaceholders[i].Visible = false;
		}
	}

	private void OnSlotButtonPressed(int slotIndex)
	{
		if (!_slotData[slotIndex].hasItem)
			return;

		ClearSelection();

		_selectedSlot = slotIndex;

		_selectedPlaceholders[slotIndex].Visible = false;
		_animationPlayers[slotIndex].Play("Show");

		var slotData = _slotData[slotIndex];
		ItemSelected?.Invoke(this, slotIndex, slotData.resourceType, slotData.quantity);
	}
	
	/// <summary>
	/// Hide all placeholders in this sector
	/// </summary>
	public void HideAllPlaceholders()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			_selectedPlaceholders[i].Visible = false;
		}
	}

	/// <summary>
	/// Clear selection in this sector
	/// </summary>
	public void ClearSelection()
	{
		HideAllPlaceholders();
		_selectedSlot = -1;
	}
	
	/// <summary>
	/// Update inventory display for this sector
	/// </summary>
	public void UpdateInventoryDisplay(Dictionary<int, (ResourceType resourceType, int quantity)> sectorResources)
	{
		// Clear all slots first
		ClearAllSlots();
		
		// Update slots with provided data
		foreach (var kvp in sectorResources)
		{
			int slotIndex = kvp.Key;
			var (resourceType, quantity) = kvp.Value;
			
			if (slotIndex >= 0 && slotIndex < SLOTS_PER_SECTOR)
			{
				SetSlotItem(slotIndex, resourceType, quantity);
			}
		}
	}
	
	/// <summary>
	/// Set item in specific slot
	/// </summary>
	private void SetSlotItem(int slotIndex, ResourceType resourceType, int quantity)
	{
		if (slotIndex < 0 || slotIndex >= SLOTS_PER_SECTOR)
			return;
		
		// Get texture from TextureManager
		var texture = TextureManager.Instance?.GetResourceIconTexture(resourceType);
		
		// Update slot data
		_slotData[slotIndex] = (resourceType, quantity, true);
		
		// Update visual components
		_itemSprites[slotIndex].Texture = texture;
		_itemSprites[slotIndex].Visible = true;
		
		_amountLabels[slotIndex].Text = quantity.ToString();
		_amountLabels[slotIndex].Visible = true;
	}
	
	/// <summary>
	/// Clear specific slot
	/// </summary>
	private void ClearSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOTS_PER_SECTOR)
			return;
		
		// Clear slot data
		_slotData[slotIndex] = (ResourceType.Wood, 0, false);
		
		// Clear visual components
		_itemSprites[slotIndex].Texture = null;
		_itemSprites[slotIndex].Visible = false;
		
		_amountLabels[slotIndex].Text = "";
		_amountLabels[slotIndex].Visible = false;
		
		_selectedPlaceholders[slotIndex].Visible = false;
	}
	
	/// <summary>
	/// Clear all slots in this sector
	/// </summary>
	private void ClearAllSlots()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			ClearSlot(i);
		}
		
		_selectedSlot = -1;
	}
	
	/// <summary>
	/// Get item data for specific slot
	/// </summary>
	public (ResourceType resourceType, int quantity, bool hasSelection) GetSlotItem(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOTS_PER_SECTOR)
			return (ResourceType.Wood, 0, false);
		
		var slotData = _slotData[slotIndex];
		return (slotData.resourceType, slotData.quantity, slotData.hasItem);
	}
	
	/// <summary>
	/// Check if this sector has any items
	/// </summary>
	public bool HasAnyItems()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			if (_slotData[i].hasItem)
				return true;
		}
		return false;
	}
	
	/// <summary>
	/// Get number of occupied slots
	/// </summary>
	public int GetOccupiedSlotCount()
	{
		int count = 0;
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			if (_slotData[i].hasItem)
				count++;
		}
		return count;
	}
}
