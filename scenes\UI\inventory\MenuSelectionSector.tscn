[gd_scene load_steps=20 format=3 uid="uid://c1jafpqv5feno"]

[ext_resource type="Script" uid="uid://bjjsvtr1wlogb" path="res://scenes/UI/inventory/MenuSelectionSector.cs" id="1_menu_selection_sector"]
[ext_resource type="Texture2D" uid="uid://cqvr3df8mwyq4" path="res://resources/solaria/UI/inventory/inventory_menu_slots.png" id="1_stenc"]
[ext_resource type="Texture2D" uid="uid://1fnqg6ox181q" path="res://resources/solaria/UI/inventory/inventory_selection.png" id="2_r0ptt"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="3_5g1ka"]
[ext_resource type="FontFile" uid="uid://brndw6mnrqxxm" path="res://fonts/Not Jam Glasgow 16.ttf" id="4_nowst"]

[sub_resource type="Animation" id="Animation_r0ptt"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectedPlaceholder:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectedPlaceholder:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.3, 1.3)]
}

[sub_resource type="Animation" id="Animation_stenc"]
resource_name = "Show"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectedPlaceholder:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectedPlaceholder:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1.2, 1.2), Vector2(1.35, 1.35), Vector2(1.3, 1.3)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_5g1ka"]
_data = {
&"RESET": SubResource("Animation_r0ptt"),
&"Show": SubResource("Animation_stenc")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_r0ptt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5g1ka"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nowst"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_h7g27"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yat41"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_dassp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jpew7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gogv0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yigny"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_46mjg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_psb2n"]

[node name="MenuSelectionSector" type="Sprite2D"]
texture = ExtResource("1_stenc")
script = ExtResource("1_menu_selection_sector")

[node name="Slot1" type="Sprite2D" parent="."]

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot1"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("2_r0ptt")

[node name="Item" type="Sprite2D" parent="Slot1"]
position = Vector2(-48, -2)
texture = ExtResource("3_5g1ka")

[node name="AmountText" type="Label" parent="Slot1"]
offset_left = -61.0
offset_top = 7.0
offset_right = 4.0
offset_bottom = 23.0
scale = Vector2(0.4, 0.4)
theme_override_colors/font_color = Color(0.278431, 0.278431, 0.278431, 1)
theme_override_fonts/font = ExtResource("4_nowst")
text = "12"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot1"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot1"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot2" type="Sprite2D" parent="."]
position = Vector2(32, 0)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot2"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("2_r0ptt")

[node name="Item" type="Sprite2D" parent="Slot2"]
position = Vector2(-48, -2)
texture = ExtResource("3_5g1ka")

[node name="AmountText" type="Label" parent="Slot2"]
offset_left = -61.0
offset_top = 7.0
offset_right = 4.0
offset_bottom = 23.0
scale = Vector2(0.4, 0.4)
theme_override_colors/font_color = Color(0.278431, 0.278431, 0.278431, 1)
theme_override_fonts/font = ExtResource("4_nowst")
text = "12"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot2"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot2"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot3" type="Sprite2D" parent="."]
position = Vector2(64, 0)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot3"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("2_r0ptt")

[node name="Item" type="Sprite2D" parent="Slot3"]
position = Vector2(-48, -2)
texture = ExtResource("3_5g1ka")

[node name="AmountText" type="Label" parent="Slot3"]
offset_left = -61.0
offset_top = 7.0
offset_right = 4.0
offset_bottom = 23.0
scale = Vector2(0.4, 0.4)
theme_override_colors/font_color = Color(0.278431, 0.278431, 0.278431, 1)
theme_override_fonts/font = ExtResource("4_nowst")
text = "12"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot3"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot3"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot4" type="Sprite2D" parent="."]
position = Vector2(96, 0)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot4"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("2_r0ptt")

[node name="Item" type="Sprite2D" parent="Slot4"]
position = Vector2(-48, -2)
texture = ExtResource("3_5g1ka")

[node name="AmountText" type="Label" parent="Slot4"]
offset_left = -61.0
offset_top = 7.0
offset_right = 4.0
offset_bottom = 23.0
scale = Vector2(0.4, 0.4)
theme_override_colors/font_color = Color(0.278431, 0.278431, 0.278431, 1)
theme_override_fonts/font = ExtResource("4_nowst")
text = "12"
horizontal_alignment = 1
vertical_alignment = 1

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot4"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot4"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")
