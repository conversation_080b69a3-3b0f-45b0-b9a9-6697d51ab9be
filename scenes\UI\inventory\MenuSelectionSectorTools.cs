using Godot;
using System;

public partial class MenuSelectionSectorTools : Sprite2D
{
	private const int SLOTS_PER_SECTOR = 10;
	
	private Sprite2D[] _slotNodes = new Sprite2D[SLOTS_PER_SECTOR];
	private Sprite2D[] _selectedPlaceholders = new Sprite2D[SLOTS_PER_SECTOR];
	private Sprite2D[] _itemSprites = new Sprite2D[SLOTS_PER_SECTOR];
	private AnimationPlayer[] _animationPlayers = new AnimationPlayer[SLOTS_PER_SECTOR];
	private Button[] _selectButtons = new Button[SLOTS_PER_SECTOR];
	
	private ToolType[] _toolSlots = new ToolType[SLOTS_PER_SECTOR];
	private int _selectedSlot = -1;
	
	public event Action<MenuSelectionSectorTools, int, ToolType> ToolSelected;
	
	private readonly ToolType[] _predefinedTools = {
		ToolType.Pickaxe,
		ToolType.Hammer, 
		ToolType.Hoe,
		ToolType.WateringCan,
		ToolType.Sword,
		ToolType.Bow,
		ToolType.None,
		ToolType.None,
		ToolType.None,
		ToolType.None
	};

	public override void _Ready()
	{
		InitializeSlotReferences();
		SetupPredefinedTools();
		ClearAllSlots();
		LoadToolsDisplay();
	}

	private void InitializeSlotReferences()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			string slotName = $"Slot{i + 1}";
			
			_slotNodes[i] = GetNode<Sprite2D>(slotName);
			_selectedPlaceholders[i] = GetNode<Sprite2D>($"{slotName}/SelectedPlaceholder");
			_itemSprites[i] = GetNode<Sprite2D>($"{slotName}/Item");
			_animationPlayers[i] = GetNode<AnimationPlayer>($"{slotName}/AnimationPlayer");
			_selectButtons[i] = GetNode<Button>($"{slotName}/SelectItemButton");
			
			int slotIndex = i;
			_selectButtons[i].Pressed += () => OnSlotButtonPressed(slotIndex);
			
			_selectedPlaceholders[i].Visible = false;
		}
	}

	private void SetupPredefinedTools()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			_toolSlots[i] = _predefinedTools[i];
		}
	}

	private void OnSlotButtonPressed(int slotIndex)
	{
		if (_toolSlots[slotIndex] == ToolType.None)
			return;
		
		ClearSelection();
		
		_selectedSlot = slotIndex;
		
		_selectedPlaceholders[slotIndex].Visible = false;
		_animationPlayers[slotIndex].Play("Show");
		
		ToolSelected?.Invoke(this, slotIndex, _toolSlots[slotIndex]);
	}

	public void HideAllPlaceholders()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			_selectedPlaceholders[i].Visible = false;
		}
	}

	public void ClearSelection()
	{
		HideAllPlaceholders();
		_selectedSlot = -1;
	}

	private void LoadToolsDisplay()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			if (_toolSlots[i] != ToolType.None)
			{
				SetSlotTool(i, _toolSlots[i]);
			}
			else
			{
				ClearSlot(i);
			}
		}
	}

	private void SetSlotTool(int slotIndex, ToolType toolType)
	{
		if (slotIndex < 0 || slotIndex >= SLOTS_PER_SECTOR)
			return;
		
		var texture = TextureManager.Instance?.GetToolTexture(toolType);
		
		_itemSprites[slotIndex].Texture = texture;
		_itemSprites[slotIndex].Visible = true;
	}

	private void ClearSlot(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOTS_PER_SECTOR)
			return;
		
		_itemSprites[slotIndex].Texture = null;
		_itemSprites[slotIndex].Visible = false;
		_selectedPlaceholders[slotIndex].Visible = false;
	}

	private void ClearAllSlots()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			ClearSlot(i);
		}
		
		_selectedSlot = -1;
	}

	public bool HasAnyTools()
	{
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			if (_toolSlots[i] != ToolType.None)
				return true;
		}
		return false;
	}

	public int GetOccupiedSlotCount()
	{
		int count = 0;
		for (int i = 0; i < SLOTS_PER_SECTOR; i++)
		{
			if (_toolSlots[i] != ToolType.None)
				count++;
		}
		return count;
	}

	public (ToolType toolType, bool hasSelection) GetSlotTool(int slotIndex)
	{
		if (slotIndex < 0 || slotIndex >= SLOTS_PER_SECTOR)
			return (ToolType.None, false);
		
		return (_toolSlots[slotIndex], _toolSlots[slotIndex] != ToolType.None);
	}
}
