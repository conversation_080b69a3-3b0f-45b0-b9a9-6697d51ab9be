[gd_scene load_steps=19 format=3 uid="uid://dqr7mvn1e4m0b"]

[ext_resource type="Texture2D" uid="uid://mc5e1na46oqm" path="res://resources/solaria/UI/inventory/inventory_menu_slots_tools.png" id="1_7wbh1"]
[ext_resource type="Script" uid="uid://j6mrp7hw3byo" path="res://scenes/UI/inventory/MenuSelectionSectorTools.cs" id="1_menu_selection_sector_tools"]
[ext_resource type="Texture2D" uid="uid://1fnqg6ox181q" path="res://resources/solaria/UI/inventory/inventory_selection.png" id="3_j4rlc"]
[ext_resource type="Texture2D" uid="uid://486dt68qu54c" path="res://resources/solaria/resources/resource_wood.png" id="4_2x7ee"]

[sub_resource type="Animation" id="Animation_r0ptt"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectedPlaceholder:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectedPlaceholder:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(1.3, 1.3)]
}

[sub_resource type="Animation" id="Animation_stenc"]
resource_name = "Show"
length = 0.09
step = 0.01
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("SelectedPlaceholder:visible")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 1,
"values": [true]
}
tracks/1/type = "value"
tracks/1/imported = false
tracks/1/enabled = true
tracks/1/path = NodePath("SelectedPlaceholder:scale")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = {
"times": PackedFloat32Array(0, 0.06, 0.09),
"transitions": PackedFloat32Array(1, 1, 1),
"update": 0,
"values": [Vector2(1.2, 1.2), Vector2(1.35, 1.35), Vector2(1.3, 1.3)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_5g1ka"]
_data = {
&"RESET": SubResource("Animation_r0ptt"),
&"Show": SubResource("Animation_stenc")
}

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_r0ptt"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_5g1ka"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_nowst"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_h7g27"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yat41"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_dassp"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_jpew7"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_gogv0"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_yigny"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_46mjg"]

[sub_resource type="StyleBoxEmpty" id="StyleBoxEmpty_psb2n"]

[node name="MenuSelectionSectorTools" type="Sprite2D"]
texture = ExtResource("1_7wbh1")
script = ExtResource("1_menu_selection_sector_tools")

[node name="Slot1" type="Sprite2D" parent="."]
position = Vector2(32, -64)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot1"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot1"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot1"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot1"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot2" type="Sprite2D" parent="."]
position = Vector2(64, -64)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot2"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot2"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot2"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot2"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot3" type="Sprite2D" parent="."]
position = Vector2(32, -32)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot3"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot3"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot3"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot3"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot4" type="Sprite2D" parent="."]
position = Vector2(64, -32)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot4"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot4"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot4"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot4"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot5" type="Sprite2D" parent="."]
position = Vector2(32, 0)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot5"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot5"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot5"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot5"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot6" type="Sprite2D" parent="."]
position = Vector2(64, 0)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot6"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot6"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot6"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot6"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot7" type="Sprite2D" parent="."]
position = Vector2(32, 32)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot7"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot7"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot7"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot7"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot8" type="Sprite2D" parent="."]
position = Vector2(64, 32)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot8"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot8"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot8"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot8"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot9" type="Sprite2D" parent="."]
position = Vector2(32, 64)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot9"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot9"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot9"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot9"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")

[node name="Slot10" type="Sprite2D" parent="."]
position = Vector2(64, 64)

[node name="SelectedPlaceholder" type="Sprite2D" parent="Slot10"]
position = Vector2(-48, 0)
scale = Vector2(1.3, 1.3)
texture = ExtResource("3_j4rlc")

[node name="Item" type="Sprite2D" parent="Slot10"]
position = Vector2(-48, 0)
texture = ExtResource("4_2x7ee")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Slot10"]
libraries = {
&"": SubResource("AnimationLibrary_5g1ka")
}

[node name="SelectItemButton" type="Button" parent="Slot10"]
offset_left = -64.0
offset_top = -16.0
offset_right = -33.0
offset_bottom = 16.0
theme_override_styles/focus = SubResource("StyleBoxEmpty_r0ptt")
theme_override_styles/disabled_mirrored = SubResource("StyleBoxEmpty_5g1ka")
theme_override_styles/disabled = SubResource("StyleBoxEmpty_nowst")
theme_override_styles/hover_pressed_mirrored = SubResource("StyleBoxEmpty_h7g27")
theme_override_styles/hover_pressed = SubResource("StyleBoxEmpty_yat41")
theme_override_styles/hover_mirrored = SubResource("StyleBoxEmpty_dassp")
theme_override_styles/hover = SubResource("StyleBoxEmpty_jpew7")
theme_override_styles/pressed_mirrored = SubResource("StyleBoxEmpty_gogv0")
theme_override_styles/pressed = SubResource("StyleBoxEmpty_yigny")
theme_override_styles/normal_mirrored = SubResource("StyleBoxEmpty_46mjg")
theme_override_styles/normal = SubResource("StyleBoxEmpty_psb2n")
