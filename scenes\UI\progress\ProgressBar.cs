using Godot;

public partial class ProgressBar : Sprite2D
{
	[Export] public Texture2D FrontTexture;
	[Export] public int TotalWidth { get; set; } = 16;
	[Export] public int TotalHeight { get; set; } = 16;
	[Export] public int MarginLeft { get; set; } = 2;
	[Export] public int MarginRight { get; set; } = 2;

	private Sprite2D _progressFront;
	private float _progress = 0.0f;

	private int UsableWidth => TotalWidth - MarginLeft - MarginRight;
	
	public override void _Ready()
	{
		_progressFront = GetNode<Sprite2D>("ProgressFront");

		if (FrontTexture != null)
		{
			_progressFront.Texture = FrontTexture;
		}

		SetProgress(1f);
	}
	
	/// <summary>
	/// Set the progress value (0.0 to 1.0)
	/// </summary>
	/// <param name="progress">Progress value between 0.0 and 1.0</param>
	public void SetProgress(float progress)
	{
		// Clamp progress between 0 and 1
		_progress = Mathf.Clamp(progress, 0.0f, 1.0f);
		
		UpdateProgressDisplay();
	}
	
	/// <summary>
	/// Get the current progress value
	/// </summary>
	/// <returns>Current progress value between 0.0 and 1.0</returns>
	public float GetProgress()
	{
		return _progress;
	}
	
	/// <summary>
	/// Update the visual display of the progress bar
	/// </summary>
	private void UpdateProgressDisplay()
	{
		if (_progressFront == null) return;

		// Calculate the width of the progress bar based on progress
		float progressWidth = _progress * UsableWidth;

		// Set the region rect to show only the portion we want
		// This crops the texture from the left side
		var region = new Rect2(0, 0, progressWidth + MarginLeft, TotalHeight);
		_progressFront.RegionEnabled = true;
		_progressFront.RegionRect = region;

		// Position the progress bar to align with the background
		// Move it left by half the difference between full width and current width
		float offsetX = -(UsableWidth - progressWidth) / 2.0f;
		_progressFront.Position = new Vector2(offsetX, 0);

		// Hide progress front if progress is 0
		_progressFront.Visible = _progress > 0.0f;
	}
	
	/// <summary>
	/// Animate progress change over time
	/// </summary>
	/// <param name="targetProgress">Target progress value (0.0 to 1.0)</param>
	/// <param name="duration">Animation duration in seconds</param>
	public void AnimateToProgress(float targetProgress, float duration = 0.5f)
	{
		targetProgress = Mathf.Clamp(targetProgress, 0.0f, 1.0f);
		
		var tween = CreateTween();
		tween.TweenMethod(Callable.From<float>(SetProgress), _progress, targetProgress, duration);
	}
	
	/// <summary>
	/// Set progress as percentage (0 to 100)
	/// </summary>
	/// <param name="percentage">Percentage value between 0 and 100</param>
	public void SetProgressPercentage(float percentage)
	{
		SetProgress(percentage / 100.0f);
	}
	
	/// <summary>
	/// Get progress as percentage (0 to 100)
	/// </summary>
	/// <returns>Progress as percentage between 0 and 100</returns>
	public float GetProgressPercentage()
	{
		return _progress * 100.0f;
	}
}
