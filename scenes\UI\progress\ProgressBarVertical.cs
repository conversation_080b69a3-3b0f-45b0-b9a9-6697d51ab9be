using Godot;

public partial class ProgressBarVertical : Sprite2D
{
	private Sprite2D _progressFront;
	private float _progress = 0.0f;
	
	// Progress bar dimensions (16x16 with 2px margins on top and bottom)
	private const int TOTAL_HEIGHT = 16;
	private const int MARGIN_TOP = 2;
	private const int MARGIN_BOTTOM = 2;
	private const int USABLE_HEIGHT = TOTAL_HEIGHT - MARGIN_TOP - MARGIN_BOTTOM; // 12px
	
	public override void _Ready()
	{
		_progressFront = GetNode<Sprite2D>("ProgressFront");
		
		SetProgress(0f);
	}
	
	/// <summary>
	/// Set the progress value (0.0 to 1.0)
	/// </summary>
	/// <param name="progress">Progress value between 0.0 and 1.0</param>
	public void SetProgress(float progress)
	{
		// Clamp progress between 0 and 1
		_progress = Mathf.Clamp(progress, 0.0f, 1.0f);
		
		UpdateProgressDisplay();
	}
	
	/// <summary>
	/// Get the current progress value
	/// </summary>
	/// <returns>Current progress value between 0.0 and 1.0</returns>
	public float GetProgress()
	{
		return _progress;
	}
	
	/// <summary>
	/// Update the visual display of the progress bar (bottom to top)
	/// </summary>
	private void UpdateProgressDisplay()
	{
		if (_progressFront == null) return;
		
		// Calculate the height of the progress bar based on progress
		float progressHeight = _progress * USABLE_HEIGHT;
		
		// Set the region rect to show only the portion we want
		// For vertical progress from bottom to top, we need to crop from the top
		float startY = TOTAL_HEIGHT - progressHeight - MARGIN_BOTTOM;
		var region = new Rect2(0, startY, TOTAL_HEIGHT, progressHeight + MARGIN_BOTTOM);
		_progressFront.RegionEnabled = true;
		_progressFront.RegionRect = region;
		
		// Position the progress bar to align with the background
		// Move it down by half the difference between full height and current height
		float offsetY = (USABLE_HEIGHT - progressHeight) / 2.0f;
		_progressFront.Position = new Vector2(0, offsetY);
		
		// Hide progress front if progress is 0
		_progressFront.Visible = _progress > 0.0f;
	}
	
	/// <summary>
	/// Animate progress change over time
	/// </summary>
	/// <param name="targetProgress">Target progress value (0.0 to 1.0)</param>
	/// <param name="duration">Animation duration in seconds</param>
	public void AnimateToProgress(float targetProgress, float duration = 0.5f)
	{
		targetProgress = Mathf.Clamp(targetProgress, 0.0f, 1.0f);
		
		var tween = CreateTween();
		tween.TweenMethod(Callable.From<float>(SetProgress), _progress, targetProgress, duration);
	}
	
	/// <summary>
	/// Set progress as percentage (0 to 100)
	/// </summary>
	/// <param name="percentage">Percentage value between 0 and 100</param>
	public void SetProgressPercentage(float percentage)
	{
		SetProgress(percentage / 100.0f);
	}
	
	/// <summary>
	/// Get progress as percentage (0 to 100)
	/// </summary>
	/// <returns>Progress as percentage between 0 and 100</returns>
	public float GetProgressPercentage()
	{
		return _progress * 100.0f;
	}
	
	/// <summary>
	/// Show the progress bar
	/// </summary>
	public void Show()
	{
		Visible = true;
	}
	
	/// <summary>
	/// Hide the progress bar
	/// </summary>
	public void Hide()
	{
		Visible = false;
	}
}
