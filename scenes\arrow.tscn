[gd_scene load_steps=6 format=3 uid="uid://b2viesaofa4te"]

[ext_resource type="Script" uid="uid://c22xtgl5p0422" path="res://scenes/Arrow.cs" id="1_arrow_script"]
[ext_resource type="Texture2D" uid="uid://tdn7v8ux3p31" path="res://resources/solaria/SpritePack/Base/Tools/Arrow/Copper Arrow Hit.png" id="2_23jat"]

[sub_resource type="Animation" id="Animation_arrow_fly"]
resource_name = "arrow_fly"
length = 0.4
loop_mode = 1
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("ArrowSprite:frame")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 0.1, 0.2, 0.3),
"transitions": PackedFloat32Array(1, 1, 1, 1),
"update": 1,
"values": [0, 1, 2, 3]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_arrow"]
_data = {
&"arrow_fly": SubResource("Animation_arrow_fly")
}

[sub_resource type="RectangleShape2D" id="RectangleShape2D_arrow"]
size = Vector2(5, 12)

[node name="Arrow" type="Area2D"]
script = ExtResource("1_arrow_script")

[node name="ArrowSprite" type="Sprite2D" parent="."]
rotation = 3.14159
scale = Vector2(0.8, 0.8)
texture = ExtResource("2_23jat")
hframes = 4
frame = 1

[node name="ArrowAnimation" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_arrow")
}

[node name="ArrowCollision" type="CollisionShape2D" parent="."]
position = Vector2(-0.5, 1)
shape = SubResource("RectangleShape2D_arrow")
