[gd_scene load_steps=4 format=3 uid="uid://bij4cmgn84lbi"]

[ext_resource type="Script" uid="uid://doenkcdr8je8x" path="res://scenes/mapObjects/AdamantiteRock.cs" id="1_adamantite_rock"]
[ext_resource type="Texture2D" uid="uid://cy0uqikeycha1" path="res://resources/solaria/exterior/adamantiteStone.png" id="2_chkco"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]

[node name="AdamantiteRock" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_adamantite_rock")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_chkco")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 5, 5, -5, 5, -7, 3, -7, -1, 7, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
