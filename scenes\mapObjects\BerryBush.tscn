[gd_scene load_steps=4 format=3 uid="uid://doox47tk4ct41"]

[ext_resource type="Script" uid="uid://cfm4iof5u5mya" path="res://scenes/mapObjects/BerryBush.cs" id="1_berrybush"]
[ext_resource type="Texture2D" uid="uid://oa7mrx5okrfg" path="res://resources/solaria/exterior/berryBush.png" id="2_berrybush"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]

[node name="BerryBush" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_berrybush")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_berrybush")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(8, 3, 5, 5, -5, 5, -8, 1, -8, -1, 8, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
