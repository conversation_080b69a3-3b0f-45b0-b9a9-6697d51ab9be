[gd_scene load_steps=3 format=3 uid="uid://dryrh52ahbyd2"]

[ext_resource type="Script" uid="uid://du8pm0o2gnfvf" path="res://scenes/mapObjects/DroppedResource.cs" id="1_dropped_resource"]

[sub_resource type="CircleShape2D" id="CircleShape2D_collection"]
radius = 8.0

[node name="DroppedResource" type="Node2D"]
z_index = 1
y_sort_enabled = true
script = ExtResource("1_dropped_resource")

[node name="Sprite2D" type="Sprite2D" parent="."]

[node name="CollectionArea" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="CollectionArea"]
shape = SubResource("CircleShape2D_collection")
