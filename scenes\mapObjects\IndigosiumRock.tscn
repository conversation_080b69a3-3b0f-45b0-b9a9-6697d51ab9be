[gd_scene load_steps=4 format=3 uid="uid://bpc6s0bqfd60n"]

[ext_resource type="Script" uid="uid://6jitl1eebyh6" path="res://scenes/mapObjects/IndigosiumRock.cs" id="1_indigosium_rock"]
[ext_resource type="Texture2D" uid="uid://bknp75wggxjmj" path="res://resources/solaria/exterior/indigosiumStone.png" id="2_sdyhw"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]

[node name="IndigosiumRock" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_indigosium_rock")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_sdyhw")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 5, 5, -5, 5, -7, 3, -7, -1, 7, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
