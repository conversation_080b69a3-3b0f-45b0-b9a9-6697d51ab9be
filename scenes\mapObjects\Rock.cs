using Godot;
using System;

/// <summary>
/// Rock object that can be destroyed by pickaxe
/// Handles hit animations, health management, and resource dropping
/// </summary>
public partial class Rock : Node2D, IDestroyableObject
{
	[Export] public int MaxHealth { get; set; } = 12;
	[Export] public ResourceType ResourceType { get; set; } = ResourceType.Stone;
	[Export] public int ResourceAmount { get; set; } = 2;

	// Event for when rock is destroyed
	[Signal] public delegate void RockDestroyedEventHandler(Vector2I tilePosition);

	private int _currentHealth;
	private Sprite2D _sprite;
	private Tween _hitTween;
	private Vector2I _tilePosition;
	private CustomDataLayerManager _customDataManager;
	private bool _isBeingDestroyed = false;
	private ProgressBar _hpBar;

	// Hit animation properties
	private readonly Color _hitColor = new Color(1.0f, 1.0f, 1.0f, 1.0f); // White tint
	private readonly Color _normalColor = new Color(1.0f, 1.0f, 1.0f, 1.0f);
	private readonly float _hitAnimationDuration = 0.3f;
	private readonly float _hitTintStrength = 0.5f; // 50% white tint

	public override void _Ready()
	{
		_currentHealth = MaxHealth;

		// Add to rocks group for duplicate detection
		AddToGroup("rocks");

		// Get sprite component
		_sprite = GetNode<Sprite2D>("Sprite2D");
		if (_sprite == null)
		{
			GD.PrintErr("Rock: Sprite2D node not found!");
			return;
		}

		// Get HP bar component
		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Rock: ProgressBar node not found!");
		}

		// Set up Y-sorting
		YSortEnabled = true;

		// Rock is 16x16, so no sprite offset needed (centered by default)
		_sprite.Position = Vector2.Zero;

		// Find CustomDataLayerManager in scene
		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Rock: CustomDataLayerManager not found!");
		}

		// Only calculate tile position if it hasn't been set by spawner
		// (SetTilePosition() is called by Region1Manager before _Ready())
		if (_tilePosition == Vector2I.Zero)
		{
			// Calculate tile position from world position (for manually placed rocks)
			_tilePosition = new Vector2I((int)(GlobalPosition.X - 8) / 16, (int)(GlobalPosition.Y - 8) / 16);

			// Mark tile as occupied (only for manually placed rocks)
			_customDataManager?.SetObjectPlaced(_tilePosition, ObjectTypePlaced.Rock);
		}

		// Initialize HP bar
		UpdateHPBar();

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}
	}

	// Rock is small (16x16) so no transparency needed when player is behind

	/// <summary>
	/// Take damage from pickaxe
	/// </summary>
	public void TakeDamage(int damage)
	{
		if (_isBeingDestroyed) return;

		_currentHealth -= damage;

		// Update HP bar
		UpdateHPBar();

		// Play hit animation
		PlayHitAnimation();

		// Check if rock should be destroyed
		if (_currentHealth <= 0)
		{
			DestroyRock();
		}
	}

	/// <summary>
	/// Play hit animation (white tint + scale effect)
	/// </summary>
	private void PlayHitAnimation()
	{
		if (_sprite == null) return;

		// Stop any existing tween
		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.SetParallel(true);

		// White tint effect
		var hitColor = _normalColor.Lerp(_hitColor, _hitTintStrength);
		_hitTween.TweenProperty(_sprite, "modulate", hitColor, _hitAnimationDuration * 0.1f);
		_hitTween.TweenProperty(_sprite, "modulate", _normalColor, _hitAnimationDuration * 0.9f)
				.SetDelay(_hitAnimationDuration * 0.1f);

		// Scale effect (smaller then bigger)
		var originalScale = _sprite.Scale;
		var smallerScale = originalScale * 0.9f;
		var biggerScale = originalScale * 1.1f;

		_hitTween.TweenProperty(_sprite, "scale", smallerScale, _hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", biggerScale, _hitAnimationDuration * 0.3f)
				.SetDelay(_hitAnimationDuration * 0.3f);
		_hitTween.TweenProperty(_sprite, "scale", originalScale, _hitAnimationDuration * 0.4f)
				.SetDelay(_hitAnimationDuration * 0.6f);
	}

	/// <summary>
	/// Destroy the rock and drop resources
	/// </summary>
	private void DestroyRock()
	{
		if (_isBeingDestroyed) return;
		_isBeingDestroyed = true;

		// Drop resources
		DropResources();

		// Clear tile occupation
		_customDataManager?.ClearObjectPlaced(_tilePosition);

		// Give XP reward
		CommonSignals.Instance?.EmitAddXp(3);

		// Emit destruction signal
		EmitSignal(SignalName.RockDestroyed, _tilePosition);

		// Remove from scene
		QueueFree();
	}

	/// <summary>
	/// Drop resources when rock is destroyed
	/// </summary>
	private void DropResources()
	{
		// Spawn multiple individual resources (each with quantity 1)
		for (int i = 0; i < ResourceAmount; i++)
		{
			// Add small random offset to prevent resources from stacking exactly
			Vector2 offset = new Vector2(
				(float)(GD.Randf() - 0.5f) * 8.0f, // Random X offset: -4 to +4 pixels
				(float)(GD.Randf() - 0.5f) * 8.0f  // Random Y offset: -4 to +4 pixels
			);
			Vector2 spawnPosition = GlobalPosition + offset;

			DroppedResource.SpawnResource(spawnPosition, ResourceType, 1);
		}
	}

	/// <summary>
	/// Get current health
	/// </summary>
	public int GetCurrentHealth()
	{
		return _currentHealth;
	}

	/// <summary>
	/// Set current health
	/// </summary>
	public void SetCurrentHealth(int health)
	{
		_currentHealth = Math.Max(0, Math.Min(health, MaxHealth));
		UpdateHPBar();
	}

	/// <summary>
	/// Get tile position
	/// </summary>
	public Vector2I GetTilePosition()
	{
		return _tilePosition;
	}

	/// <summary>
	/// Set tile position (used by spawner)
	/// </summary>
	public void SetTilePosition(Vector2I position)
	{
		_tilePosition = position;
		// Position object in the center of the tile (tile size is 16x16)
		// Rock is 16x16, so no visual offset needed
		GlobalPosition = new Vector2(position.X * 16 + 8, position.Y * 16 + 8);
	}

	/// <summary>
	/// Check if this rock can be hit by pickaxe from given position
	/// </summary>
	public bool CanBeHitFrom(Vector2I playerTilePosition)
	{
		// Rock can be hit if player is adjacent (including diagonally)
		var distance = _tilePosition - playerTilePosition;
		return Math.Abs(distance.X) <= 1 && Math.Abs(distance.Y) <= 1;
	}

	/// <summary>
	/// Update HP bar based on current health
	/// </summary>
	private void UpdateHPBar()
	{
		if (_hpBar == null) return;

		// Calculate health percentage
		float healthPercentage = (float)_currentHealth / MaxHealth;

		// If at full health, hide HP bar
		if (_currentHealth >= MaxHealth)
		{
			_hpBar.Hide();
		}
		else
		{
			// Show HP bar and set progress
			_hpBar.Show();
			_hpBar.SetProgress(healthPercentage);
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (_tilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}
}
