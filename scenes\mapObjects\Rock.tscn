[gd_scene load_steps=4 format=3 uid="uid://rvxuixamqy64"]

[ext_resource type="Script" uid="uid://bpe120vsllby5" path="res://scenes/mapObjects/Rock.cs" id="1_rock"]
[ext_resource type="Texture2D" uid="uid://vichnqplaqwn" path="res://resources/solaria/exterior/stone_1.png" id="2_stone"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_xb8u3"]

[node name="Rock" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_rock")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_stone")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionPolygon2D" type="CollisionPolygon2D" parent="StaticBody2D"]
polygon = PackedVector2Array(7, 3, 2, 5, -7, 3, -7, -1, 7, -1)

[node name="ProgressBar" parent="." instance=ExtResource("3_xb8u3")]
position = Vector2(0, 7)
scale = Vector2(1, 0.6)
