[gd_scene load_steps=5 format=3 uid="uid://dmixu0hi2qu1p"]

[ext_resource type="Script" uid="uid://cowctgdy25gbq" path="res://scenes/mapObjects/Tree.cs" id="1_8k7xj"]
[ext_resource type="Texture2D" uid="uid://dsku8x8sqdhg3" path="res://resources/solaria/exterior/tree_1.png" id="2_3h8vk"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_bb27y"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_lgydw"]
size = Vector2(6, 6)

[node name="Tree" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_8k7xj")

[node name="Sprite2D" type="Sprite2D" parent="."]
texture = ExtResource("2_3h8vk")

[node name="Area2D" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="Area2D"]

[node name="StaticBody2D" type="StaticBody2D" parent="."]
position = Vector2(0, 8)

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0, 11)
shape = SubResource("RectangleShape2D_lgydw")

[node name="ProgressBar" parent="." instance=ExtResource("3_bb27y")]
position = Vector2(0, 24)
scale = Vector2(1, 0.6)
