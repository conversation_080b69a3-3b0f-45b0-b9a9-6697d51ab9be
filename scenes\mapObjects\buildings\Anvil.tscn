[gd_scene load_steps=8 format=3 uid="uid://nvil123"]

[ext_resource type="Script" uid="uid://coxtlb4uru8ym" path="res://scenes/mapObjects/buildings/Anvil.cs" id="1_anvil"]
[ext_resource type="Texture2D" uid="uid://8dd4tcykr04k" path="res://resources/solaria/buildings/anvil_foreground.png" id="3_g13e6"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_gh3ed"]
[ext_resource type="PackedScene" uid="uid://daufcx5a20v8v" path="res://scenes/UI/buildingMenus/AnvilMenu.tscn" id="4_g13e6"]
[ext_resource type="PackedScene" uid="uid://b8xf7h2lam3pq" path="res://scenes/UI/progress/ProgressBarVertical.tscn" id="5_crafting_progress"]

[sub_resource type="RectangleShape2D" id="RectangleShape2D_usjcl"]
size = Vector2(15, 9)

[sub_resource type="CircleShape2D" id="CircleShape2D_gh3ed"]

[node name="Anvil" type="Node2D"]
y_sort_enabled = true
script = ExtResource("1_anvil")

[node name="AnvilSprite" type="Sprite2D" parent="."]
y_sort_enabled = true
texture = ExtResource("3_g13e6")

[node name="CraftingResource" type="Sprite2D" parent="."]
y_sort_enabled = true
position = Vector2(1, 18)
scale = Vector2(0.75, 0.75)
offset = Vector2(0, -30)

[node name="StaticBody2D" type="StaticBody2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="StaticBody2D"]
position = Vector2(0.5, 3.5)
shape = SubResource("RectangleShape2D_usjcl")

[node name="ProgressBar" parent="." instance=ExtResource("3_gh3ed")]
position = Vector2(0.4, 9)
scale = Vector2(1.05, 0.6)

[node name="PlayerDetector" type="Area2D" parent="."]

[node name="CollisionShape2D" type="CollisionShape2D" parent="PlayerDetector"]
position = Vector2(1, 1)
shape = SubResource("CircleShape2D_gh3ed")

[node name="AnvilMenu" parent="." instance=ExtResource("4_g13e6")]

[node name="ProgressBarVertical" parent="." instance=ExtResource("5_crafting_progress")]
visible = false
z_index = 1
position = Vector2(16, -1)
