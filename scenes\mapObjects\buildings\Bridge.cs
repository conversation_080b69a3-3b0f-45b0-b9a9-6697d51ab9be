using Godot;

public partial class Bridge : Node2D
{
	[Export] public ObjectType BuildingType { get; set; } = ObjectType.Bridge;

	private const int TILE_SIZE = 16;
	private const int BUILDING_WIDTH = 1;
	private const int BUILDING_HEIGHT = 1;
	private const int MAX_HEALTH = 8;

	private Vector2I _topLeftTilePosition;
	private bool _isPlaced = false;
	private bool _isBeingDestroyed = false;
	private int _currentHealth;
	private string _saveId;

	private Sprite2D _bridgeSprite;
	private ProgressBar _hpBar;
	private CustomDataLayerManager _customDataManager;
	private TileMapLayer _bridgeTileMapLayer;

	private Color _normalColor = Colors.White;
	private Color _invalidColor = new Color(1.0f, 0.5f, 0.5f, 0.8f);

	private Tween _hitTween;

	public override void _Ready()
	{
		_currentHealth = MAX_HEALTH;

		// Set Z-index to ensure bridge is always behind player
		ZIndex = -10;

		_bridgeSprite = GetNode<Sprite2D>("BridgeSprite");
		if (_bridgeSprite == null)
		{
			GD.PrintErr("Bridge: BridgeSprite node not found!");
			return;
		}

		_hpBar = GetNode<ProgressBar>("ProgressBar");
		if (_hpBar == null)
		{
			GD.PrintErr("Bridge: ProgressBar node not found!");
		}

		if (_isPlaced)
		{
			_bridgeSprite.Modulate = _normalColor;
		}

		_customDataManager = GetNode<CustomDataLayerManager>("/root/world/CustomDataLayerManager");
		if (_customDataManager == null)
		{
			GD.PrintErr("Bridge: CustomDataLayerManager not found!");
		}

		_bridgeTileMapLayer = GetNode<TileMapLayer>("/root/world/Layer2Floor_Bridge_SpeedModifier");
		if (_bridgeTileMapLayer == null)
		{
			GD.PrintErr("Bridge: Bridge tilemap layer not found!");
		}

		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed += OnPickaxeUsed;
		}

		if (_hpBar != null)
		{
			_hpBar.SetProgress(1.0f);
			_hpBar.Hide();
		}
	}

	public void SetTilePosition(Vector2I topLeftTile)
	{
		_topLeftTilePosition = topLeftTile;

		float centerX = (topLeftTile.X + 0.5f) * TILE_SIZE;
		float centerY = (topLeftTile.Y + 0.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);
	}

	public Vector2I GetTopLeftTilePosition()
	{
		return _topLeftTilePosition;
	}

	public bool CanBePlacedAt(Vector2I topLeftTile)
	{
		if (_customDataManager == null || _bridgeTileMapLayer == null) return false;

		Vector2I tilePos = topLeftTile;
		var tileData = _customDataManager.GetTileData(tilePos);

		// Debug output to see what's happening
		GD.Print($"Bridge: Checking placement at {tilePos} - CanBridge: {tileData.CanBridge}, ObjectPlaced: {tileData.ObjectTypePlaced}");

		// Bridge can ONLY be placed on tiles that specifically have CanBridge = true
		if (!tileData.CanBridge)
		{
			GD.Print($"Bridge: Cannot place - CanBridge is false at {tilePos}");
			return false;
		}

		// Also check that no other object is already placed there
		if (tileData.ObjectTypePlaced != 0)
		{
			GD.Print($"Bridge: Cannot place - Object already placed at {tilePos}: {tileData.ObjectTypePlaced}");
			return false;
		}

		GD.Print($"Bridge: Can place at {tilePos}");
		return true;
	}

	public void PlaceBuilding()
	{
		if (_customDataManager == null || _bridgeTileMapLayer == null || _isPlaced) return;

		var resourcesManager = ResourcesManager.Instance;
		if (resourcesManager != null)
		{
			bool hasPlank = resourcesManager.RemoveResource(ResourceType.Plank, 5);
			bool hasBeam = resourcesManager.RemoveResource(ResourceType.WoodenBeam, 2);

			if (!hasPlank || !hasBeam)
			{
				GD.PrintErr("Bridge: Failed to consume resources for building!");
				return;
			}
		}

		Vector2I tilePos = _topLeftTilePosition;

		// Save original water tile
		int originalTileId = _bridgeTileMapLayer.GetCellSourceId(tilePos);
		Vector2I originalAtlasCoords = _bridgeTileMapLayer.GetCellAtlasCoords(tilePos);

		// Place bridge tile (source 1, atlas coords 0,0)
		_bridgeTileMapLayer.SetCell(tilePos, 1, Vector2I.Zero);

		_customDataManager.SetObjectPlaced(tilePos, ObjectTypePlaced.Building);
		_customDataManager.SetBridgeOriginalTile(tilePos, originalTileId, originalAtlasCoords);

		_isPlaced = true;
		if (_bridgeSprite != null)
		{
			_bridgeSprite.Modulate = _normalColor;
		}

		_saveId = ResourcesManager.Instance?.AddBuilding(_topLeftTilePosition, "Bridge", (int)BuildingType);

		GD.Print($"Bridge: Building placed at {_topLeftTilePosition} (consumed 5 planks, 2 beams)");
	}

	public void DestroyBuilding()
	{
		if (_customDataManager == null || _bridgeTileMapLayer == null || !_isPlaced || _isBeingDestroyed) return;
		_isBeingDestroyed = true;

		Vector2I tilePos = _topLeftTilePosition;
		
		// Restore original water tile
		var originalTileData = _customDataManager.GetBridgeOriginalTile(tilePos);
		_bridgeTileMapLayer.SetCell(tilePos, originalTileData.SourceId, originalTileData.AtlasCoords);

		_customDataManager.ClearObjectPlaced(tilePos);
		_customDataManager.ClearBridgeOriginalTile(tilePos);

		if (!string.IsNullOrEmpty(_saveId))
		{
			ResourcesManager.Instance?.RemoveBuildingById(_saveId);
		}

		_hitTween?.Kill();
		_hitTween = null;
		_bridgeSprite = null;
		_customDataManager = null;
		_bridgeTileMapLayer = null;
		_isPlaced = false;
		_saveId = null;

		QueueFree();
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.PickaxeUsed -= OnPickaxeUsed;
		}
	}

	public void SetPlacementFeedback(bool canPlace)
	{
		Color targetColor = canPlace ? _normalColor : _invalidColor;
		if (_bridgeSprite != null)
		{
			_bridgeSprite.Modulate = targetColor;
		}
	}

	private void OnPickaxeUsed(Vector2I tilePosition, int damage)
	{
		if (!_isPlaced) return;

		if (_topLeftTilePosition == tilePosition)
		{
			Vector2I playerTile = GetPlayerTilePosition();
			if (CanBeHitFrom(playerTile, tilePosition))
			{
				TakeDamage(damage);
			}
		}
	}

	private Vector2I GetPlayerTilePosition()
	{
		var player = GetNode<PlayerController>("/root/world/Player");
		if (player != null)
		{
			return new Vector2I(
				Mathf.FloorToInt(player.GlobalPosition.X / 16),
				Mathf.FloorToInt(player.GlobalPosition.Y / 16)
			);
		}
		return Vector2I.Zero;
	}

	private bool CanBeHitFrom(Vector2I playerTile, Vector2I targetTile)
	{
		int distance = Mathf.Abs(playerTile.X - targetTile.X) + Mathf.Abs(playerTile.Y - targetTile.Y);
		return distance <= 2;
	}

	private void TakeDamage(int damage)
	{
		_currentHealth = Mathf.Max(0, _currentHealth - damage);

		if (_hpBar != null)
		{
			float healthPercentage = (float)_currentHealth / MAX_HEALTH;
			_hpBar.SetProgress(healthPercentage);

			// Hide health bar when at full health
			if (_currentHealth >= MAX_HEALTH)
			{
				_hpBar.Hide();
			}
			else
			{
				_hpBar.Show();
			}
		}

		ShowHitEffect();

		if (_currentHealth <= 0)
		{
			DestroyBuilding();
		}
	}

	private void ShowHitEffect()
	{
		if (_bridgeSprite == null) return;

		_hitTween?.Kill();
		_hitTween = CreateTween();
		_hitTween.TweenProperty(_bridgeSprite, "modulate", Colors.Red, 0.1f);
		_hitTween.TweenProperty(_bridgeSprite, "modulate", _normalColor, 0.1f);
	}

	public void LoadFromSaveData(BuildingData buildingData)
	{
		_saveId = buildingData.Id;
		_topLeftTilePosition = new Vector2I(buildingData.TopLeftX, buildingData.TopLeftY);
		BuildingType = (ObjectType)buildingData.BuildingId;

		float centerX = (_topLeftTilePosition.X + 0.5f) * TILE_SIZE;
		float centerY = (_topLeftTilePosition.Y + 0.5f) * TILE_SIZE;
		GlobalPosition = new Vector2(centerX, centerY);

		_isPlaced = true;
		if (_bridgeSprite != null)
		{
			_bridgeSprite.Modulate = _normalColor;
		}
	}
}
