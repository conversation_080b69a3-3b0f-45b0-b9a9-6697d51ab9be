[gd_scene load_steps=3 format=3 uid="uid://bridge123"]

[ext_resource type="Script" path="res://scenes/mapObjects/buildings/Bridge.cs" id="1_bridge"]
[ext_resource type="Texture2D" uid="uid://bsrmiu50aadu8" path="res://resources/solaria/buildings/bridge.png" id="2_bridge_texture"]
[ext_resource type="PackedScene" uid="uid://otpfc634hhga" path="res://scenes/UI/progress/ProgressBar.tscn" id="3_progress"]

[node name="Bridge" type="Node2D"]
y_sort_enabled = false
z_index = -10
script = ExtResource("1_bridge")

[node name="BridgeSprite" type="Sprite2D" parent="."]
z_index = -10
texture = ExtResource("2_bridge_texture")

[node name="ProgressBar" parent="." instance=ExtResource("3_progress")]
position = Vector2(0, -12)
scale = Vector2(1.0, 0.6)
