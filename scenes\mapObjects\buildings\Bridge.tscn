[gd_scene load_steps=4 format=3 uid="uid://bridge123"]

[ext_resource type="Script" path="res://scenes/mapObjects/buildings/Bridge.cs" id="1_bridge"]
[ext_resource type="Texture2D" uid="uid://bsrmiu50aadu8" path="res://resources/solaria/buildings/bridge.png" id="2_bridge_texture"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(1, 0, 0, 0.8)

[node name="Bridge" type="Node2D"]
script = ExtResource("1_bridge")

[node name="BridgeSprite" type="Sprite2D" parent="."]
texture = ExtResource("2_bridge_texture")

[node name="ProgressBar" type="ProgressBar" parent="."]
offset_left = -16.0
offset_top = -24.0
offset_right = 16.0
offset_bottom = -20.0
theme_override_styles/fill = SubResource("StyleBoxFlat_1")
max_value = 8.0
value = 8.0
show_percentage = false
