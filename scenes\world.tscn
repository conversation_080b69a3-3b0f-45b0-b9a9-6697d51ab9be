[gd_scene load_steps=27 format=4 uid="uid://dq0nxjiipwin0"]

[ext_resource type="Texture2D" uid="uid://yv0oau5oc2l" path="res://resources/solaria/exterior/Terrain.png" id="1_nnsk1"]
[ext_resource type="Texture2D" uid="uid://dby826kpimo7m" path="res://resources/MapLayers/MAP_SPEED_MODIFIER.png" id="2_4wyf3"]
[ext_resource type="PackedScene" uid="uid://io5tjlaupxaq" path="res://scenes/player.tscn" id="2_rwgxs"]
[ext_resource type="PackedScene" uid="uid://bvn8x70a01a2b" path="res://scenes/CustomDataLayerManager.tscn" id="3_k0juu"]
[ext_resource type="PackedScene" uid="uid://cw5lj8bgfsyaj" path="res://scenes/DroppedResourceManager.tscn" id="4_dropped_resource_manager"]
[ext_resource type="Texture2D" uid="uid://uo7h5ut4grwa" path="res://resources/MapLayers/CAN_DESTROYABLE_OBJECT.png" id="5_qfnet"]
[ext_resource type="Texture2D" uid="uid://cd4enhwjmg2rh" path="res://resources/MapLayers/MAP_REGIONS.png" id="6_i7141"]
[ext_resource type="PackedScene" uid="uid://cxvn8ywqxhqy" path="res://scenes/regions/Region1Manager.tscn" id="7_7r4gi"]
[ext_resource type="PackedScene" uid="uid://doox47tk4ct41" path="res://scenes/mapObjects/BerryBush.tscn" id="8_dss4m"]
[ext_resource type="PackedScene" uid="uid://c13q3mm3n1etg" path="res://scenes/UI/inventory/SelectedToolPanel.tscn" id="9_7r4gi"]
[ext_resource type="PackedScene" uid="uid://dumldq4wvv7y2" path="res://scenes/mapObjects/Rock2.tscn" id="9_7t5mc"]
[ext_resource type="PackedScene" uid="uid://c7pcpuwwc2e7k" path="res://scenes/UI/inventory/InventoryMenu.tscn" id="10_w7kh3"]
[ext_resource type="PackedScene" uid="uid://bmvk4ryge08cs" path="res://scenes/BuildingPlacer.tscn" id="11_buildingplacer"]
[ext_resource type="PackedScene" uid="uid://bnxwvx42a6onm" path="res://scenes/UI/PlayerStatusPanel.tscn" id="11_jhx03"]
[ext_resource type="PackedScene" path="res://scenes/BuildingManager.tscn" id="12_buildingmanager"]
[ext_resource type="PackedScene" uid="uid://cunpkoexg6ab7" path="res://scenes/mapObjects/animals/Rabbit.tscn" id="14_o8fc1"]

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_fj7yv"]
texture = ExtResource("1_nnsk1")
12:8/0 = 0
13:8/0 = 0
14:9/0 = 0
14:8/0 = 0
12:9/0 = 0
13:9/0 = 0
12:10/0 = 0
13:10/0 = 0
14:10/0 = 0
15:8/0 = 0
16:8/0 = 0
16:9/0 = 0
15:9/0 = 0
15:10/0 = 0
16:10/0 = 0
17:15/0 = 0
17:14/0 = 0
17:13/0 = 0
18:13/0 = 0
19:14/0 = 0
19:13/0 = 0
19:15/0 = 0
18:14/0 = 0
18:15/0 = 0
20:13/0 = 0
21:14/0 = 0
20:14/0 = 0
21:13/0 = 0
0:62/0 = 0
0:62/0/custom_data_0 = 1
2:62/0 = 0
2:62/0/custom_data_0 = 3
1:62/0 = 0
1:62/0/custom_data_0 = 2
3:62/0 = 0
3:62/0/custom_data_0 = 4
22:14/0 = 0
21:15/0 = 0
22:16/0 = 0
21:16/0 = 0
22:15/0 = 0
20:16/0 = 0

[sub_resource type="TileSet" id="TileSet_tlwt5"]
custom_data_layer_0/name = "region"
custom_data_layer_0/type = 2
sources/0 = SubResource("TileSetAtlasSource_fj7yv")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_k0juu"]
texture = ExtResource("2_4wyf3")
0:0/0 = 0
0:0/0/physics_layer_0/polygon_0/points = PackedVector2Array(-11.0383, -7.99799, 10.4318, -8.09364, 10.4588, 7.96588, -11.0319, 7.96589)
0:0/0/custom_data_0 = 1.0
0:1/0 = 0
0:1/0/custom_data_0 = 0.7
0:2/0 = 0
0:3/0 = 0
0:4/0 = 0
0:5/0 = 0
0:6/0 = 0
0:7/0 = 0
0:8/0 = 0
0:9/0 = 0
0:10/0 = 0
0:11/0 = 0

[sub_resource type="TileSet" id="TileSet_71j4m"]
physics_layer_0/collision_layer = 1
custom_data_layer_0/name = "speedModifier"
custom_data_layer_0/type = 3
sources/0 = SubResource("TileSetAtlasSource_k0juu")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_4mrxx"]
texture = ExtResource("5_qfnet")
0:0/0 = 0
0:0/0/custom_data_0 = true

[sub_resource type="TileSet" id="TileSet_qfnet"]
custom_data_layer_0/name = "canDestroyableObject"
custom_data_layer_0/type = 1
sources/0 = SubResource("TileSetAtlasSource_4mrxx")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_7r4gi"]
texture = ExtResource("5_qfnet")
0:0/0 = 0

[sub_resource type="TileSet" id="TileSet_i7141"]
custom_data_layer_0/name = "objectTypePlaced"
custom_data_layer_0/type = 2
sources/0 = SubResource("TileSetAtlasSource_7r4gi")

[sub_resource type="TileSetAtlasSource" id="TileSetAtlasSource_i7141"]
texture = ExtResource("6_i7141")
0:0/0 = 0
0:0/0/custom_data_0 = 1

[sub_resource type="TileSet" id="TileSet_4mrxx"]
custom_data_layer_0/name = "region"
custom_data_layer_0/type = 2
sources/0 = SubResource("TileSetAtlasSource_i7141")

[node name="world" type="Node2D"]
y_sort_enabled = true

[node name="Layer1Ground" type="TileMapLayer" parent="."]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_tlwt5")

[node name="Layer2Floor_Bridge_SpeedModifier" type="TileMapLayer" parent="."]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_71j4m")

[node name="CustomDataLayerManager" parent="." node_paths=PackedStringArray("SpeedModifierLayer", "CanBuildingLayer", "CanDestroyableObjectLayer", "CanPlantLayer", "CanEnemyLayer", "CanBridgeLayer", "CanFishingLayer", "ObjectTypePlacedLayer", "RegionLayer") instance=ExtResource("3_k0juu")]
SpeedModifierLayer = NodePath("../Layer2Floor_Bridge_SpeedModifier")
CanBuildingLayer = NodePath("LayerCanBuilding")
CanDestroyableObjectLayer = NodePath("LayerCanDestroyableObject")
CanPlantLayer = NodePath("LayerCanPlant")
CanEnemyLayer = NodePath("LayerCanEnemy")
CanBridgeLayer = NodePath("LayerCanBridge")
CanFishingLayer = NodePath("LayerCanFishing")
ObjectTypePlacedLayer = NodePath("LayerObjectTypePlaced")
RegionLayer = NodePath("LayerRegion")

[node name="LayerCanBuilding" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerCanDestroyableObject" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_qfnet")

[node name="LayerCanEnemy" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerCanPlant" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerCanBridge" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerCanFishing" type="TileMapLayer" parent="CustomDataLayerManager"]

[node name="LayerObjectTypePlaced" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_i7141")

[node name="LayerRegion" type="TileMapLayer" parent="CustomDataLayerManager"]
tile_map_data = PackedByteArray("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")
tile_set = SubResource("TileSet_4mrxx")

[node name="LayerYSort" type="TileMapLayer" parent="."]
y_sort_enabled = true

[node name="Buildings" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Player" parent="." node_paths=PackedStringArray("CustomDataManager") instance=ExtResource("2_rwgxs")]
position = Vector2(61, 71)
CustomDataManager = NodePath("../CustomDataLayerManager")

[node name="RegionManagers" type="Node2D" parent="."]
y_sort_enabled = true

[node name="Region1Manager" parent="RegionManagers" instance=ExtResource("7_7r4gi")]
BaseSpawnInterval = 10.0
BerryBushScene = ExtResource("8_dss4m")
Rock2Scene = ExtResource("9_7t5mc")
RabbitSpawnInterval = 5.0
RabbitScene = ExtResource("14_o8fc1")

[node name="DroppedResourceManager" parent="." instance=ExtResource("4_dropped_resource_manager")]

[node name="SelectedToolPanel" parent="." instance=ExtResource("9_7r4gi")]

[node name="InventoryMenu" parent="." instance=ExtResource("10_w7kh3")]

[node name="PlayerStatusPanel" parent="." instance=ExtResource("11_jhx03")]

[node name="BuildingPlacer" parent="." instance=ExtResource("11_buildingplacer")]

[node name="BuildingManager" parent="." instance=ExtResource("12_buildingmanager")]
