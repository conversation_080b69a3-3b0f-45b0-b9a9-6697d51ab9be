using Godot;
using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

/// <summary>
/// Quick select item data for the selected tool panel
/// </summary>
[System.Serializable]
public class QuickSelectItem
{
    public bool IsEmpty { get; set; } = true;
    public bool IsTool { get; set; } = false;
    public ToolType ToolType { get; set; } = ToolType.None;
    public ResourceType ResourceType { get; set; } = ResourceType.Wood;
    public int Quantity { get; set; } = 0;
}

/// <summary>
/// Player statistics data
/// </summary>
[System.Serializable]
public class PlayerStats
{
    public float Health { get; set; } = 10.0f;
    public float MaxHealth { get; set; } = 10.0f;
    public float Energy { get; set; } = 10.0f;
    public float MaxEnergy { get; set; } = 10.0f;
    public float Food { get; set; } = 10.0f;
    public float MaxFood { get; set; } = 10.0f;
    public float Water { get; set; } = 10.0f;
    public float MaxWater { get; set; } = 10.0f;
    public int Money { get; set; } = 0;
    public int Level { get; set; } = 1;
    public int Experience { get; set; } = 0;
    public int PerkPoints { get; set; } = 0;
    public Vector2 Position { get; set; } = Vector2.Zero;
    public string LastDirection { get; set; } = "down";
    public ToolType SelectedTool { get; set; } = ToolType.None;
    public List<QuickSelectItem> QuickSelectItems { get; set; } = new List<QuickSelectItem>();
    public int MaxInventorySlots { get; set; } = 8;
    public Dictionary<ToolType, int> ToolLevels { get; set; } = new Dictionary<ToolType, int>();
}

/// <summary>
/// Player inventory and resources
/// </summary>
[System.Serializable]
public class PlayerResources
{
    public Dictionary<ResourceType, int> Resources { get; set; } = new Dictionary<ResourceType, int>();

    /// <summary>
    /// Add a resource to inventory with max quantity per slot limit
    /// </summary>
    public void AddResource(ResourceType resourceType, int quantity = 1)
    {
        const int MAX_QUANTITY_PER_SLOT = 9999;

        if (Resources.ContainsKey(resourceType))
        {
            int newQuantity = Resources[resourceType] + quantity;
            Resources[resourceType] = Math.Min(newQuantity, MAX_QUANTITY_PER_SLOT);
        }
        else
        {
            Resources[resourceType] = Math.Min(quantity, MAX_QUANTITY_PER_SLOT);
        }
    }

    /// <summary>
    /// Remove a resource from inventory (always use slot with less resources first)
    /// </summary>
    public bool RemoveResource(ResourceType resourceType, int quantity = 1)
    {
        if (!Resources.ContainsKey(resourceType) || Resources[resourceType] < quantity)
            return false;

        Resources[resourceType] -= quantity;
        if (Resources[resourceType] <= 0)
            Resources.Remove(resourceType);

        return true;
    }

    /// <summary>
    /// Get resource quantity
    /// </summary>
    public int GetResourceQuantity(ResourceType resourceType)
    {
        return Resources.ContainsKey(resourceType) ? Resources[resourceType] : 0;
    }

    /// <summary>
    /// Check if player has enough of a resource
    /// </summary>
    public bool HasResource(ResourceType resourceType, int quantity = 1)
    {
        return GetResourceQuantity(resourceType) >= quantity;
    }
}

/// <summary>
/// Dropped resource data for save/load
/// </summary>
[System.Serializable]
public class DroppedResourceData
{
    public string Id { get; set; } = System.Guid.NewGuid().ToString();
    public float X { get; set; }
    public float Y { get; set; }
    public ResourceType ResourceType { get; set; }
    public int Quantity { get; set; }
}

/// <summary>
/// Building data for save/load
/// </summary>
[System.Serializable]
public class BuildingData
{
    public string Id { get; set; } = System.Guid.NewGuid().ToString();
    public string BuildingType { get; set; } = "Anvil";
    public int TopLeftX { get; set; }
    public int TopLeftY { get; set; }
    public int BuildingId { get; set; } = (int)ObjectType.Anvil; // ObjectType value
    public int CurrentHealth { get; set; } = 20;

    // Anvil-specific crafting data
    public int SelectedCraftingResource { get; set; } = 0; // ResourceType as int (0 = None)
    public int CraftingProgress { get; set; } = 0;
}

/// <summary>
/// World/map data from CustomDataLayerManager
/// </summary>
[System.Serializable]
public class WorldData
{
    public Dictionary<string, object> TileData { get; set; } = new Dictionary<string, object>();
    public Dictionary<string, object> PlacedObjects { get; set; } = new Dictionary<string, object>();
    public Dictionary<string, object> CustomLayerData { get; set; } = new Dictionary<string, object>();
    public List<DroppedResourceData> DroppedResources { get; set; } = new List<DroppedResourceData>();
    public List<BuildingData> Buildings { get; set; } = new List<BuildingData>();
    public DateTime LastSaved { get; set; } = DateTime.Now;
}

/// <summary>
/// Game settings and preferences
/// </summary>
[System.Serializable]
public class GameSettings
{
    public float MasterVolume { get; set; } = 1.0f;
    public float MusicVolume { get; set; } = 1.0f;
    public float SfxVolume { get; set; } = 1.0f;
    public bool Fullscreen { get; set; } = false;
    public string Language { get; set; } = TranslationServer.GetLocale();
    public Dictionary<string, object> CustomSettings { get; set; } = new Dictionary<string, object>();
}

/// <summary>
/// Complete game save data
/// </summary>
[System.Serializable]
public class GameSaveData
{
    public PlayerStats PlayerStats { get; set; } = new PlayerStats();
    public PlayerResources PlayerResources { get; set; } = new PlayerResources();
    public WorldData WorldData { get; set; } = new WorldData();
    public GameSettings GameSettings { get; set; } = new GameSettings();
    public DateTime SaveTime { get; set; } = DateTime.Now;
    public string SaveVersion { get; set; } = "1.0.0";
    public int PlayTimeSeconds { get; set; } = 0;

    /// <summary>
    /// Validate save data integrity
    /// </summary>
    public bool IsValid()
    {
        return PlayerStats != null &&
               PlayerResources != null &&
               WorldData != null &&
               GameSettings != null;
    }
}

/// <summary>
/// Event arguments for resource changes
/// </summary>
public class ResourceChangedEventArgs : EventArgs
{
    public ResourceType ResourceType { get; set; }
    public int OldValue { get; set; }
    public int NewValue { get; set; }
    public int ChangeAmount { get; set; }
}

/// <summary>
/// Event arguments for stat changes
/// </summary>
public class StatChangedEventArgs : EventArgs
{
    public string StatName { get; set; }
    public float OldValue { get; set; }
    public float NewValue { get; set; }
    public float ChangeAmount { get; set; }
}