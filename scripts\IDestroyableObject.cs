using Godot;

/// <summary>
/// Interface for objects that can be destroyed by tools (trees, rocks, etc.)
/// </summary>
public interface IDestroyableObject
{
	/// <summary>
	/// Take damage from a tool
	/// </summary>
	/// <param name="damage">Amount of damage to take</param>
	void TakeDamage(int damage);

	/// <summary>
	/// Check if this object can be hit from the given position
	/// </summary>
	/// <param name="playerTilePosition">Player's tile position</param>
	/// <returns>True if the object can be hit from this position</returns>
	bool CanBeHitFrom(Vector2I playerTilePosition);

	/// <summary>
	/// Get the tile position of this object
	/// </summary>
	/// <returns>Tile position</returns>
	Vector2I GetTilePosition();

	/// <summary>
	/// Get the current health of this object
	/// </summary>
	/// <returns>Current health</returns>
	int GetCurrentHealth();

	/// <summary>
	/// Set the current health of this object
	/// </summary>
	/// <param name="health">Health to set</param>
	void SetCurrentHealth(int health);
}
