using Godot;
using System.Collections.Generic;

public enum DescriptionType
{
    TextOnly
}

public class ItemInfo
{
    public string Title { get; set; }
    public string Description { get; set; }
    public DescriptionType DescriptionType { get; set; }
    public bool CanBeUsed { get; set; }
    public bool CanAssignToQuickUse { get; set; }
}

public static class ItemInformation
{
    private static readonly Dictionary<ResourceType, ItemInfo> _resourceInfo = new Dictionary<ResourceType, ItemInfo>
    {
        {
            ResourceType.Wood,
            new ItemInfo
            {
                Title = "TEXT_WOOD_TITLE",
                Description = "TEXT_WOOD_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.Stone,
            new ItemInfo
            {
                Title = "TEXT_STONE_TITLE",
                Description = "TEXT_STONE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.Net,
            new ItemInfo
            {
                Title = "TEXT_NET_TITLE",
                Description = "TEXT_NET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.Plank,
            new ItemInfo
            {
                Title = "PLANK_TEXT",
                Description = "PLANK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.Stone2,
            new ItemInfo
            {
                Title = "STONE2_TEXT",
                Description = "STONE2_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.Berry,
            new ItemInfo
            {
                Title = "BERRY_TEXT",
                Description = "BERRY_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = true,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.Leaf,
            new ItemInfo
            {
                Title = "LEAF_TEXT",
                Description = "LEAF_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.CopperOre,
            new ItemInfo
            {
                Title = "COPPER_ORE_TEXT",
                Description = "COPPER_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.IronOre,
            new ItemInfo
            {
                Title = "IRON_ORE_TEXT",
                Description = "IRON_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.GoldOre,
            new ItemInfo
            {
                Title = "GOLD_ORE_TEXT",
                Description = "GOLD_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.IndigosiumOre,
            new ItemInfo
            {
                Title = "INDIGOSIUM_ORE_TEXT",
                Description = "INDIGOSIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.MithrilOre,
            new ItemInfo
            {
                Title = "MITHRIL_ORE_TEXT",
                Description = "MITHRIL_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.ErithrydiumOre,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_ORE_TEXT",
                Description = "ERITHRYDIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.AdamantiteOre,
            new ItemInfo
            {
                Title = "ADAMANTITE_ORE_TEXT",
                Description = "ADAMANTITE_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.UraniumOre,
            new ItemInfo
            {
                Title = "URANIUM_ORE_TEXT",
                Description = "URANIUM_ORE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.CopperBar,
            new ItemInfo
            {
                Title = "COPPER_BAR_TEXT",
                Description = "COPPER_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.IronBar,
            new ItemInfo
            {
                Title = "IRON_BAR_TEXT",
                Description = "IRON_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.GoldBar,
            new ItemInfo
            {
                Title = "GOLD_BAR_TEXT",
                Description = "GOLD_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.IndigosiumBar,
            new ItemInfo
            {
                Title = "INDIGOSIUM_BAR_TEXT",
                Description = "INDIGOSIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.MithrilBar,
            new ItemInfo
            {
                Title = "MITHRIL_BAR_TEXT",
                Description = "MITHRIL_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.ErithrydiumBar,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_BAR_TEXT",
                Description = "ERITHRYDIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.AdamantiteBar,
            new ItemInfo
            {
                Title = "ADAMANTITE_BAR_TEXT",
                Description = "ADAMANTITE_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.UraniumBar,
            new ItemInfo
            {
                Title = "URANIUM_BAR_TEXT",
                Description = "URANIUM_BAR_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.CopperSheet,
            new ItemInfo
            {
                Title = "COPPER_SHEET_TEXT",
                Description = "COPPER_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.IronSheet,
            new ItemInfo
            {
                Title = "IRON_SHEET_TEXT",
                Description = "IRON_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.GoldSheet,
            new ItemInfo
            {
                Title = "GOLD_SHEET_TEXT",
                Description = "GOLD_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.IndigosiumSheet,
            new ItemInfo
            {
                Title = "INDIGOSIUM_SHEET_TEXT",
                Description = "INDIGOSIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.MithrilSheet,
            new ItemInfo
            {
                Title = "MITHRIL_SHEET_TEXT",
                Description = "MITHRIL_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.ErithrydiumSheet,
            new ItemInfo
            {
                Title = "ERITHRYDIUM_SHEET_TEXT",
                Description = "ERITHRYDIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.AdamantiteSheet,
            new ItemInfo
            {
                Title = "ADAMANTITE_SHEET_TEXT",
                Description = "ADAMANTITE_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.UraniumSheet,
            new ItemInfo
            {
                Title = "URANIUM_SHEET_TEXT",
                Description = "URANIUM_SHEET_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.WoodenBeam,
            new ItemInfo
            {
                Title = "WOODEN_BEAM_TEXT",
                Description = "WOODEN_BEAM_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ResourceType.WoodenStick,
            new ItemInfo
            {
                Title = "STICK_TEXT",
                Description = "STICK_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        }
    };

    private static readonly Dictionary<ToolType, ItemInfo> _toolInfo = new Dictionary<ToolType, ItemInfo>
    {
        {
            ToolType.Pickaxe,
            new ItemInfo
            {
                Title = "TEXT_PICKAXE_TITLE",
                Description = "TEXT_PICKAXE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Sword,
            new ItemInfo
            {
                Title = "TEXT_SWORD_TITLE",
                Description = "TEXT_SWORD_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Bow,
            new ItemInfo
            {
                Title = "TEXT_BOW_TITLE",
                Description = "TEXT_BOW_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Hammer,
            new ItemInfo
            {
                Title = "TEXT_HAMMER_TITLE",
                Description = "TEXT_HAMMER_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.Hoe,
            new ItemInfo
            {
                Title = "TEXT_HOE_TITLE",
                Description = "TEXT_HOE_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        },
        {
            ToolType.WateringCan,
            new ItemInfo
            {
                Title = "TEXT_WATERING_CAN_TITLE",
                Description = "TEXT_WATERING_CAN_DESCRIPTION",
                DescriptionType = DescriptionType.TextOnly,
                CanBeUsed = false,
                CanAssignToQuickUse = true
            }
        }
    };

    public static ItemInfo GetResourceInfo(ResourceType resourceType)
    {
        return _resourceInfo.TryGetValue(resourceType, out var info) ? info : new ItemInfo
        {
            Title = "TEXT_UNKNOWN_ITEM_TITLE",
            Description = "TEXT_UNKNOWN_ITEM_DESCRIPTION",
            DescriptionType = DescriptionType.TextOnly,
            CanBeUsed = false,
            CanAssignToQuickUse = false
        };
    }

    public static ItemInfo GetToolInfo(ToolType toolType)
    {
        return _toolInfo.TryGetValue(toolType, out var info) ? info : new ItemInfo
        {
            Title = "TEXT_UNKNOWN_TOOL_TITLE",
            Description = "TEXT_UNKNOWN_TOOL_DESCRIPTION",
            DescriptionType = DescriptionType.TextOnly,
            CanBeUsed = false,
            CanAssignToQuickUse = false
        };
    }

    public static bool CanBeUsed(ResourceType resourceType)
    {
        return GetResourceInfo(resourceType).CanBeUsed;
    }

    public static bool CanBeUsed(ToolType toolType)
    {
        return GetToolInfo(toolType).CanBeUsed;
    }

    public static bool CanAssignToQuickUse(ResourceType resourceType)
    {
        return GetResourceInfo(resourceType).CanAssignToQuickUse;
    }

    public static bool CanAssignToQuickUse(ToolType toolType)
    {
        return GetToolInfo(toolType).CanAssignToQuickUse;
    }
}
