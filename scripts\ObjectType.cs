using Godot;

/// <summary>
/// Enum for different object types that can be placed on tiles
/// Used with objectTypePlaced custom data property
/// </summary>
public enum ObjectType
{
	Empty = 0,

	// Resource Objects (1-19)
	Tree = 1,
	Rock = 2,
	BerryBush = 3,
	Rock2 = 4,
	CopperRock = 5,
	IronRock = 6,
	GoldRock = 7,
	IndigosiumRock = 8,
	MithrilRock = 9,
	ErithrydiumRock = 10,
	AdamantiteRock = 11,
	UraniumRock = 12,

	Bridge = 13,

	// Buildings (20-39)
	Anvil = 20,

	// // Plants (40-59)
	// Plant_Wheat = 40,
	// Plant_Carrot = 41,
	// Plant_Potato = 42,

	// // Decorations (60-79)
	// Decoration_Torch = 60,
	// Decoration_Fence = 61,

	// // Special Objects (80-99)
	// Bridge = 80,
	// Portal = 81
}

/// <summary>
/// Helper methods for ObjectType enum
/// </summary>
public static class ObjectTypeExtensions
{


	/// <summary>
	/// Get the amount of resource this object provides when harvested
	/// </summary>
	public static int GetResourceAmount(this ObjectType objectType)
	{
		return objectType switch
		{
			ObjectType.Tree => 3, // Trees give 3 wood
			ObjectType.Rock => 2, // Rocks give 2 stone
			ObjectType.BerryBush => 2, // Berry bushes give 2 berries
			ObjectType.Rock2 => 2, // Sandstone rocks give 2 sandstone
			ObjectType.CopperRock => 2, // Copper rocks give 2 copper ore
			ObjectType.IronRock => 2, // Iron rocks give 2 iron ore
			ObjectType.GoldRock => 2, // Gold rocks give 2 gold ore
			ObjectType.IndigosiumRock => 2, // Indigosium rocks give 2 indigosium ore
			ObjectType.MithrilRock => 2, // Mithril rocks give 2 mithril ore
			ObjectType.ErithrydiumRock => 2, // Erithrydium rocks give 2 erithrydium ore
			ObjectType.AdamantiteRock => 2, // Adamantite rocks give 2 adamantite ore
			ObjectType.UraniumRock => 2, // Uranium rocks give 2 uranium ore
			ObjectType.Anvil => 0, // Buildings don't drop resources when destroyed
			_ => 1 // Default amount
		};
	}

	/// <summary>
	/// Get the maximum health for this object type
	/// </summary>
	public static int GetMaxHealth(this ObjectType objectType)
	{
		return objectType switch
		{
			ObjectType.Tree => 8,
			ObjectType.Rock => 12,
			ObjectType.BerryBush => 4,
			ObjectType.Rock2 => 12,
			ObjectType.CopperRock => 12,
			ObjectType.IronRock => 12,
			ObjectType.GoldRock => 12,
			ObjectType.IndigosiumRock => 12,
			ObjectType.MithrilRock => 12,
			ObjectType.ErithrydiumRock => 12,
			ObjectType.AdamantiteRock => 12,
			ObjectType.UraniumRock => 12,
			ObjectType.Anvil => 20, // Buildings are very durable
			_ => 5 // Default health
		};
	}

	/// <summary>
	/// Check if this object type is a building
	/// </summary>
	public static bool IsBuilding(this ObjectType objectType)
	{
		return objectType switch
		{
			ObjectType.Anvil => true,
			_ => false
		};
	}

	/// <summary>
	/// Check if this object type is a resource object (can be harvested)
	/// </summary>
	public static bool IsResourceObject(this ObjectType objectType)
	{
		return objectType switch
		{
			ObjectType.Tree => true,
			ObjectType.Rock => true,
			ObjectType.BerryBush => true,
			ObjectType.Rock2 => true,
			ObjectType.CopperRock => true,
			ObjectType.IronRock => true,
			ObjectType.GoldRock => true,
			ObjectType.IndigosiumRock => true,
			ObjectType.MithrilRock => true,
			ObjectType.ErithrydiumRock => true,
			ObjectType.AdamantiteRock => true,
			ObjectType.UraniumRock => true,
			_ => false
		};
	}
}
