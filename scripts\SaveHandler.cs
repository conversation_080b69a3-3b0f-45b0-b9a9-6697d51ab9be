using Godot;
using System;
using System.Text.Json;
using System.Text.Json.Serialization;

/// <summary>
/// Static class for handling save/load operations with JSON serialization
/// </summary>
public static class SaveHandler
{
    private static readonly string SaveDirectory = "user://saves/";

    /// <summary>
    /// JSON serializer options for consistent formatting
    /// </summary>
    private static readonly JsonSerializerOptions JsonOptions = new JsonSerializerOptions
    {
        WriteIndented = false,  // Compact JSON without whitespace for smaller file size
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        Converters = { new JsonStringEnumConverter() },
        IncludeFields = true
    };

    static SaveHandler()
    {
        // Ensure save directory exists
        if (!DirAccess.DirExistsAbsolute(SaveDirectory))
        {
            DirAccess.Open("user://").MakeDir("saves");
        }
    }

    /// <summary>
    /// Save data to a JSON file
    /// </summary>
    /// <typeparam name="T">Type of data to save</typeparam>
    /// <param name="data">Data to save</param>
    /// <param name="filename">Filename (without extension)</param>
    /// <returns>True if save was successful</returns>
    public static bool Save<T>(T data, string filename)
    {
        try
        {
            string filePath = $"{SaveDirectory}{filename}.json";
            string jsonString = JsonSerializer.Serialize(data, JsonOptions);

            using var file = FileAccess.Open(filePath, FileAccess.ModeFlags.Write);
            if (file == null)
            {
                GD.PrintErr($"Failed to open file for writing: {filePath}");
                return false;
            }

            file.StoreString(jsonString);
            file.Close();

            return true;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error saving data to {filename}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Load data from a JSON file
    /// </summary>
    /// <typeparam name="T">Type of data to load</typeparam>
    /// <param name="filename">Filename (without extension)</param>
    /// <returns>Loaded data or default(T) if failed</returns>
    public static T Load<T>(string filename)
    {
        try
        {
            string filePath = $"{SaveDirectory}{filename}.json";

            if (!Godot.FileAccess.FileExists(filePath))
            {
                return default(T);
            }

            using var file = Godot.FileAccess.Open(filePath, Godot.FileAccess.ModeFlags.Read);
            if (file == null)
            {
                GD.PrintErr($"Failed to open file for reading: {filePath}");
                return default(T);
            }

            string jsonString = file.GetAsText();
            file.Close();

            T data = JsonSerializer.Deserialize<T>(jsonString, JsonOptions);
            return data;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error loading data from {filename}: {ex.Message}");
            return default(T);
        }
    }

    /// <summary>
    /// Check if a save file exists
    /// </summary>
    /// <param name="filename">Filename (without extension)</param>
    /// <returns>True if file exists</returns>
    public static bool SaveExists(string filename)
    {
        string filePath = $"{SaveDirectory}{filename}.json";
        return Godot.FileAccess.FileExists(filePath);
    }

    /// <summary>
    /// Delete a save file
    /// </summary>
    /// <param name="filename">Filename (without extension)</param>
    /// <returns>True if deletion was successful</returns>
    public static bool DeleteSave(string filename)
    {
        try
        {
            string filePath = $"{SaveDirectory}{filename}.json";

            if (!Godot.FileAccess.FileExists(filePath))
            {
                return false;
            }

            DirAccess.Open("user://").Remove(filePath);
            return true;
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error deleting save file {filename}: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Get all available save files
    /// </summary>
    /// <returns>Array of save filenames (without extensions)</returns>
    public static string[] GetAvailableSaves()
    {
        try
        {
            var dir = DirAccess.Open(SaveDirectory);
            if (dir == null)
                return new string[0];

            var saves = new System.Collections.Generic.List<string>();
            dir.ListDirBegin();

            string fileName = dir.GetNext();
            while (fileName != "")
            {
                if (fileName.EndsWith(".json"))
                {
                    saves.Add(fileName.Replace(".json", ""));
                }
                fileName = dir.GetNext();
            }

            return saves.ToArray();
        }
        catch (Exception ex)
        {
            GD.PrintErr($"Error getting available saves: {ex.Message}");
            return new string[0];
        }
    }
}
