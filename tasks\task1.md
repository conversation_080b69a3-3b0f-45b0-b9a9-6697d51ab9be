Your tasks:
1. Read technical.md file which contains most things you should know about design of the game.
2. in texture manager add following resources and appropriate icons: copper, iron, gold, indigosium, mithril, erithrydium, adamantite, uranium - each one is ore so add for example IronOreTexture and IronOreIconTexture
3. as we have basic  resources added in point 2 (they are ores of metals) add corresponding: metal bar and metal sheet - so for example IronBarTexture and IronBarIconTexture, IronSheetTexture and IronSheetIconTexture etc - but for all of that ores
4. don't set their images - i will set them later
5. look at Rock.tscn - i want you to duplicate this scene and create a separate scene and script for: Copper, Iron, Gold, indigosium, mithril, erithrydium, adamantite, uranium. call them CopperRock etc accordingly. they need to work the same like Rock.tscn but need to drop according resource - copper, iron, gold (ores) etc. Don't set sprite texture of these new scenes - for now just copy the rock texture, but i will set their textures later.
6. only create scenes and scripts, don't use these scenes in the game yet
7. at the end update technical.md