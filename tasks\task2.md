Your tasks:
1. when player hit sword then signal is emited when animation ended. i want the signal to be emited when attack animation is in the middle, not at the end - add if this is possible
2. add delay to player bow shooting - max once per 1.5s
3. player has energy, needs food and water. when player use any tool/weapon then he should get -1 energy. there should be a delay of 5s when player doesn't use a tool then energy starts restoring - 1 energy per 1s and every time it increases then this time is reduced by 0.1s (max speed of restoring should be 1 per 0,1s). when player start using a tool then this energy 5s delay should reset. also, every 2 actions player loses 1 food and every 3 actions 1 water. when player has 0 food or water then movement and animations speed and energy restoration should decrease by 40% and if food AND water is 0 then player speed and animations and energy restoring should decrease by 60%. if player has 0 food or 0 water and doesn't restore some of it for 30s then player should die (for now don't implement die just create a method). if player has 0 water or food then i want to have some kind of hud (a modulate on full screen, a bit red and transparent - to show that player is in danger) - create some nice looking for this. this should also allow to add food/water/health. there should be a separate scene in autoload to handle this player energy, water, food and health. also, you can add or use existing signal from common signals. implement a logic that will add 5 food and 3 water when player use (eat) a berrie. add minimal changes to player logic and more in new autoload.
4. add delay to player usage of berry - max 1 per 3s
7. at the end update technical.md