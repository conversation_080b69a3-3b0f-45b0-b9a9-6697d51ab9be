your tasks:
1. in resources->solaria->buildings->bridge.png we have bridge image. 
in BuildMenu scene i want to add another ItemList - BridgeItemList. 
cost of building bridge will be 5 wooden plank + 2 wooden beam. 
when player selects to build bridge then i want to have similar mechanism to placing Anvil BUT bridge is 1x1 tile.
 bridge should be handled like this: in world scene we have Layer2Floor_Bridge_SpeedModifier tile map layer. 
 it has 2 tile sources defined: one is for the colliders so that player won't be able to walk on water (its on source 0) but there are different water with ground connections so you need to remember which atlas coordinates it has. 
 then on source 1 we have a single tile - bridge (source 1, atlas (0,0)) - so when player builds a bridge it has to remember which tile from source 0 was befaure because if player destroy bridge then this old tile should be restore (with collider, from source 0). 
 when bridge built - add info in object type placed. now, player can destroy bridge with pickaxe - bridge has 8 hp (so we need to add a health bar simiar like in anvil - but only show when bridge has less than full hp).
 so to conclude: player can build bridge, it appears on Layer2Floor_Bridge_SpeedModifier, previous tile needs to be saved because when bridge destroyed, it needs to be restored. bridge should be loaded/saved when game closes or autosave -just like other save things. bridge has no colliders added so player can walk on it.
 bridge can be build when given tile has CanBridge and has no other building type built there (object type placed) (CanBuilding is not required for bridge).
 actually i added ItemListBridge with bridge so just use it - implement handle button.
 2. rename ItemList from BuildMenu scene to ItemListAnvil and handle accordingly in script