tasks:
1. i can't see the red overlay when player doesnt have food and water that you were supposed to add. when player has 0 food and 0 water then he only slows down but the red overlay is not shown. fix it.
2. i get error: E 0:00:42:614   NodeExtensions.cs:47 @ T Godot.Node.GetNode<T>(Godot.NodePath): System.InvalidCastException: Unable to cast object of type 'Godot.TileMapLayer' to type 'Godot.TileMap'.
  <C# Error>    System.InvalidCastException
  <C# Source>   /root/godot/modules/mono/glue/GodotSharp/GodotSharp/Core/Extensions/NodeExtensions.cs:47 @ T Godot.Node.GetNode<T>(Godot.NodePath)
  <Stack Trace> NodeExtensions.cs:47 @ T Godot.Node.GetNode<T>(Godot.NodePath)
                Bridge.cs:56 @ void Bridge._Ready()
                Node.cs:2546 @ bool Godot.Node.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                CanvasItem.cs:1654 @ bool Godot.CanvasItem.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                Node2D.cs:557 @ bool Godot.Node2D.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                Bridge_ScriptMethods.generated.cs:158 @ bool Bridge.InvokeGodotClassMethod(Godot.NativeInterop.godot_string_name&, Godot.NativeInterop.NativeVariantPtrArgs, Godot.NativeInterop.godot_variant&)
                CSharpInstanceBridge.cs:24 @ Godot.NativeInterop.godot_bool Godot.Bridge.CSharpInstanceBridge.Call(nint, Godot.NativeInterop.godot_string_name*, Godot.NativeInterop.godot_variant**, int, Godot.NativeInterop.godot_variant_call_error*, Godot.NativeInterop.godot_variant*)
3. health bar of bridge should be hidden when bridge has full hp (also initially when placing)
4. i can't place bridge - maybe because of exception above?
5. at the end update technical.md